from django.shortcuts import render, HttpResponse
from django.http import JsonResponse
import csv
import os
import subprocess
from django.shortcuts import render
from django.http import HttpResponse
from .models import Client, ClientList
from .forms import ClientForm
from asgiref.sync import sync_to_async
import csv
import os
import subprocess
csv

def check_ip_blacklist(ip):
    try:
        response = subprocess.run(
            ['ping', '-c', '4', ip],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=10
        )
        if response.returncode == 0:
            return ip, response.stdout.decode()
        else:
            return ip, response.stderr.decode() or "Ping failed"
    except subprocess.TimeoutExpired:
        return ip, "Request timed out"

def check_blacklist_status(ip_list):
    results = {}
    for ip in ip_list:
        ip_result = check_ip_blacklist(ip)
        results[ip_result[0]] = ip_result[1]
    return results

def fetch_items(request, client):
    items = {
        'client1': [('item1', 'Item 1'), ('item2', 'Item 2')],
        'client2': [('item3', 'Item 3'), ('item4', 'Item 4')],
        'client3': [('item5', 'Item 5'), ('item6', 'Item 6')],
    }
    client_items = items.get(client, [])
    return JsonResponse({'items': client_items})

def process_csv_file(file):
    ip_list = []
    decoded_file = file.read().decode('utf-8').splitlines()
    reader = csv.reader(decoded_file)
    for row in reader:
        ip_list.extend(row)
    return ip_list

def ip_address_view(request):
    if request.method == 'POST':
        form = IPAddressForm(request.POST, request.FILES)
        if form.is_valid():
            client = form.cleaned_data.get('name')
            item = form.cleaned_data.get('item')
            ip_addresses = form.cleaned_data.get('ip_addresses')
            csv_file = form.cleaned_data.get('csv_file')
            ip_list = []

            if ip_addresses:
                ip_list.extend(ip_addresses.split())

            if csv_file:
                ip_list.extend(process_csv_file(csv_file))

            if client != 'None':
                results = check_blacklist_status(ip_list)
                return render(request, 'result.html', {'results': results})
            else:
                return render(request, 'result.html', {'results': {}})  # No API call if 'None' is selected

    else:
        form = IPAddressForm()

    return render(request, 'form.html', {'form': form})


# Create your views here.
def home(request):
    return HttpResponse("Hello World !!")

def homepage(request):
    return render(request, "base.html")

def blank(request):
    return render(request, "blank.html")

def noaccess(request):
    return render(request, "restricted.html")

def select_client(request):
    form = ClientForm()
    #print("rendering form")
    # if request.method == 'GET' and 'client' in request.GET:
    #     client_id = request.GET.get('client')  # Ensure we're using get() method
    #     if client_id:
    #         form.fields['clientlist'].queryset = ClientList.objects.filter(client_id=client_id)

    return render(request, 'select_client_list.html', {'form': form})

def load_clientlist(request):
    client_id = request.GET.get('client')
    clientlist = ClientList.objects.filter(client_id=client_id).order_by('name')
    return render(request, 'clientlist_dropdown_list_options.html', {'clientlist': clientlist})