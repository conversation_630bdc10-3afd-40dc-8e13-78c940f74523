import os
import requests
import pandas as pd
import json
from django.shortcuts import render
from urllib.parse import urljoin
from django.http import HttpResponse
from akamai.edgegrid import EdgeGridAuth, EdgeRc
from .models import APIUser
from django.core.management import call_command

def delete_user_calls():
    APIUser.objects.all().delete()

def welcome(request):
    apiUser = APIUser()
    print(20 * "*=", apiUser.name)
    print(20 * "*=", apiUser.accountSwitching)
    context = {'apiUser':apiUser}
    return render(request,"welcome.html",context)

