# import os
# from django.shortcuts import render
# from django.http import HttpResponse
# from akamai.edgegrid import EdgeGridAuth, EdgeRc
#
# edgerc = EdgeRc('C:\\Users\\<USER>\\.edgerc2')
# section = 'default'
# baseurl = 'https://%s' % edgerc.get(section, 'host')
# hostname = edgerc.get(section, 'host')
# client_secret = edgerc.get(section, 'client_secret')
# access_token  = edgerc.get(section, 'access_token')
# client_token  = edgerc.get(section, 'client_token')
#
#
# def read_file_view(request):
#     # Get the user's home directory
#     home_dir = os.path.expanduser('~')
#     file_path = os.path.join(home_dir, 'xxx')
#
#     try:
#         with open(file_path, 'r') as file:
#             file_content = file.read()
#         return HttpResponse(file_content, content_type='text/plain')
#     except FileNotFoundError:
#         return HttpResponse("File not found.", content_type='text/plain')
#     except Exception as e:
#         return HttpResponse(f"An error occurred: {e}", content_type='text/plain')
