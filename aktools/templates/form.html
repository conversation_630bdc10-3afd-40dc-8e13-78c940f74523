<!DOCTYPE html>
<html>
<head>
    <title>IP Address Form</title>
    <script>
        async function fetchItems(clientId) {
            const response = await fetch(`/fetch-items/${clientId}/`);
            const data = await response.json();
            const itemSelect = document.getElementById('id_item');
            itemSelect.innerHTML = '';

            data.items.forEach(item => {
                const option = document.createElement('option');
                option.value = item[0];
                option.text = item[1];
                itemSelect.appendChild(option);
            });
        }

        function onClientChange() {
            const clientSelect = document.getElementById('id_client');
            const selectedClientId = clientSelect.value;
            const itemField = document.getElementById('id_item');

            if (selectedClientId === '') {
                itemField.parentElement.style.display = 'none';
            } else {
                itemField.parentElement.style.display = 'block';
                fetchItems(selectedClientId);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const clientSelect = document.getElementById('id_client');
            const itemField = document.getElementById('id_item');
            itemField.parentElement.style.display = 'none'; // Hide the item dropdown