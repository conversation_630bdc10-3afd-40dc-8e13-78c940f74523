```mermaid
graph TD
    %% Main Components
    User[User/Client]
    Django[Django Web Framework]
    
    %% Django Apps
    AuditorApp[Auditor App]
    ClientApp[Client App]
    UserApp[User App]
    HtmxApp[HTMX Tests App]
    
    %% External Services
    AkamaiAPI[Akamai API]
    
    %% Models
    AuditResults[AuditResults Model]
    Client[Client Model]
    ClientList[ClientList Model]
    ClientListItems[ClientListItems Model]
    UserInput[UserInput Model]
    APIUser[APIUser Model]
    
    %% Key Functions
    VerificationModule[Verification Module]
    ProcessFile[Process File Function]
    CheckBlacklist[Check Blacklist Function]
    ExtractIPs[Extract IPs Function]
    
    %% Data Stores
    PDFReports[PDF Reports]
    Database[(Database)]
    
    %% User Interactions
    User -->|Uploads PDF| AuditorApp
    User -->|Views Results| AuditorApp
    User -->|Manages Blacklists| ClientApp
    
    %% App Relationships
    Django -->|Contains| AuditorApp
    Django -->|Contains| ClientApp
    Django -->|Contains| UserApp
    Django -->|Contains| HtmxApp
    
    %% Auditor App Flow
    AuditorApp -->|Uses| ProcessFile
    AuditorApp -->|Creates/Updates| AuditResults
    ProcessFile -->|Extracts Data From| PDFReports
    ProcessFile -->|Calls| ExtractIPs
    AuditResults -->|Stored In| Database
    
    %% Verification Flow
    AuditorApp -->|Uses| VerificationModule
    VerificationModule -->|Calls| CheckBlacklist
    CheckBlacklist -->|Queries| ClientListItems
    
    %% Client App Flow
    ClientApp -->|Manages| Client
    ClientApp -->|Manages| ClientList
    ClientApp -->|Manages| ClientListItems
    Client -->|Stored In| Database
    ClientList -->|Stored In| Database
    ClientListItems -->|Stored In| Database
    
    %% User App Flow
    UserApp -->|Manages| APIUser
    APIUser -->|Stored In| Database
    
    %% External API Interactions
    UserApp -->|Authenticates With| AkamaiAPI
    ClientApp -->|Retrieves Data From| AkamaiAPI
    
    %% Data Flow
    AuditResults -->|Contains| ExtractIPs
    ExtractIPs -->|Validates Against| ClientListItems
    
    %% Subgraphs for organization
    subgraph "Data Processing"
        ProcessFile
        ExtractIPs
        CheckBlacklist
        VerificationModule
    end
    
    subgraph "Models"
        AuditResults
        Client
        ClientList
        ClientListItems
        UserInput
        APIUser
    end
    
    %% Style
    classDef app fill:#f9f,stroke:#333,stroke-width:2px;
    classDef model fill:#bbf,stroke:#333,stroke-width:1px;
    classDef function fill:#bfb,stroke:#333,stroke-width:1px;
    classDef external fill:#fbb,stroke:#333,stroke-width:1px;
    
    class AuditorApp,ClientApp,UserApp,HtmxApp app;
    class AuditResults,Client,ClientList,ClientListItems,UserInput,APIUser model;
    class VerificationModule,ProcessFile,CheckBlacklist,ExtractIPs function;
    class AkamaiAPI external;
```
