from akamai.edgegrid import EdgeGridAuth, EdgeRc
import requests
import os
import logging

def open_akamai_session():
    """
    Establishes an authenticated session with the Akamai API.
    
    This function reads Akamai API credentials from the .edgerc2 file in the user's
    home directory, creates a requests Session object, and configures it with
    EdgeGrid authentication required by Akamai APIs.
    
    Returns:
        tuple: A 5-element tuple containing:
            - s (requests.Session): The authenticated session object
            - baseurl (str): The base URL for API calls
            - hostname (str): The Akamai API hostname
            - edgerc (EdgeRc): The EdgeRc configuration object
            - section (str): The configuration section used (default: 'default')
    """
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        home_dir = os.path.expanduser('~')
        file_path = os.path.join(str(home_dir), '.edgerc2')
        
        logger.info(f"Attempting to read .edgerc2 file from: {file_path}")
        
        if not os.path.exists(file_path):
            logger.error(f".edgerc2 file not found at {file_path}")
            raise FileNotFoundError(f".edgerc2 file not found at {file_path}")
        
        edgerc = EdgeRc(file_path)
        section = 'default'
        
        # Check if the section exists
        if not edgerc.has_section(section):
            available_sections = edgerc.sections()
            logger.error(f"Section '{section}' not found in .edgerc2. Available sections: {available_sections}")
            if available_sections:
                section = available_sections[0]
                logger.info(f"Using alternative section: {section}")
            else:
                raise ValueError("No valid sections found in .edgerc2 file")
        
        hostname = edgerc.get(section, 'host')
        baseurl = 'https://%s' % hostname
        
        logger.info(f"Successfully read .edgerc2 file. Using host: {hostname}")
        
        s = requests.Session()
        s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
        
        return s, baseurl, hostname, edgerc, section
    except Exception as e:
        logger.error(f"Error in open_akamai_session: {str(e)}")
        raise


def close_akamai_session(session):
    """
    Properly closes an Akamai API session.
    
    This function ensures that the requests Session is properly closed,
    releasing any system resources associated with it.
    
    Args:
        session (requests.Session): The session object to close
    """
    session.close()

# API endpoint for account switch keys
# account_switch_keys = f"https://{hostname}/identity-management/v3/api-clients/self/account-switch-keys"