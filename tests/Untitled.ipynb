#%%
import os

import requests
from akamai.edgegrid import EdgeGridAuth, EdgeRc
#%%
global edgerc, section, hostname, s
home_dir = os.path.expanduser('~')
file_path = os.path.join(str(home_dir), '.edgerc')
edgerc = EdgeRc(file_path)
section = 'default'
hostname = edgerc.get(section, 'host')
baseurl = 'https://%s' % hostname
s = requests.Session()
s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
#%%
profile_url = f"https://{hostname}/identity-management/v3/user-profile"
self_url = f"https://{hostname}/identity-management/v3/api-clients/self"
switch_keys_url = f"https://{hostname}/identity-management/v3/api-clients/self/account-switch-keys"
accessible_client_lists_url = f'https://{hostname}/client-list/v1/lists'

#https://techdocs.akamai.com/client-lists/reference/get-list
get_list_items = f'https://{hostname}/client-list/v1/lists/<listId>/items'
#%%
listid = "181061_DGFIPIPBLOCKLIST"
#%%
# Construct the API URL using the client_list_id
api_url = get_list_items.replace("<listId>", listid)
api_url
querystring = {
    "actions": True,
    "authGrants": True,
    "notifications": True,
    "accountSwitchKey": "F-AC-5015532:1-5G3LB"
}
response = s.get(api_url, params=querystring)
#%%
response.json()
#%%
