{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container mt-4" id="upload">
    <h1>Upload Directory for Audit</h1>

    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="card">
        <div class="card-body">
            <form hx-post="{% url 'auditor:upload_file' %}" hx-swap="outerHTML" hx-target="#upload" method="post">
                {% csrf_token %}
                <div class="mb-3">
                    <label class="form-label" for="directory_path">Directory Path</label>
                    <input class="form-control" id="directory_path" name="directory_path" placeholder="Enter the full path to the directory"
                           required type="text">
                    <div class="form-text">
                        Enter the full path to the directory you want to audit.
                    </div>
                </div>

                <button class="btn btn-primary" type="submit">
                    Start Audit
                </button>
            </form>

            <div class="mt-3" id="result">
                <!-- HTMX will replace this div with the response -->
            </div>
        </div>
    </div>
</div>
{% endblock %}