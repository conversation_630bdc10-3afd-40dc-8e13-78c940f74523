{% load static %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header text-white" style="background-color: #ff6600;">
                    <h4 class="mb-0">Processing Files</h4>
                </div>
                <div class="card-body">
                    <!-- Progress content will be updated via JavaScript -->
                    <div id="progress-content">
                        <div class="progress mb-3">
                            <div id="progress-bar" 
                                 class="progress-bar bg-success" 
                                 role="progressbar" 
                                 style="width: {{ percentage }}%;" 
                                 aria-valuenow="{{ percentage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                {{ percentage }}%
                            </div>
                        </div>

                        <div id="status-text" class="alert alert-info">
                            <strong>Status:</strong> {{ status }}
                        </div>

                        <div id="last-updated" class="small text-muted text-end">
                            Last updated: {% now "H:i:s" %}
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="{% url 'auditor:audit-results-list' %}" class="btn btn-secondary mr-2">View Results</a>
                        <button id="stop-button" class="btn btn-danger">
                            Stop Processing
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Poll for progress updates every 2 seconds
    const progressBar = document.getElementById('progress-bar');
    const statusText = document.getElementById('status-text');
    const lastUpdatedText = document.getElementById('last-updated');
    const stopButton = document.getElementById('stop-button');
    
    // Add event listener for stop button
    stopButton.addEventListener('click', function() {
        fetch('{% url "auditor:stop_audit" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            statusText.innerHTML = '<strong>Status:</strong> ' + data.message;
            // Force a final progress update
            updateProgress();
        })
        .catch(error => {
            console.error('Error stopping audit:', error);
        });
    });
    
    function updateProgress() {
        console.log("Fetching progress update...");
        
        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        const url = `{% url "auditor:audit_progress" %}?t=${timestamp}`;
        
        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            console.log("Response status:", response.status);
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log("Progress data:", data);
            
            // Update progress bar
            progressBar.style.width = data.percentage + '%';
            progressBar.setAttribute('aria-valuenow', data.percentage);
            progressBar.textContent = data.percentage + '%';
            
            // Update status text
            statusText.innerHTML = '<strong>Status:</strong> ' + data.status;
            
            // Update last updated time
            const now = new Date();
            const timeString = now.toTimeString().split(' ')[0];
            lastUpdatedText.textContent = 'Last updated: ' + timeString;
            
            // If not complete, schedule another update
            if (!data.complete) {
                setTimeout(updateProgress, 2000);
            } else {
                console.log("Processing complete, redirecting to results page");
                window.location.href = '{% url "auditor:audit-results-list" %}';
            }
        })
        .catch(error => {
            console.error('Error fetching progress:', error);
            // Continue polling even if there's an error
            setTimeout(updateProgress, 5000);
        });
    }
    
    // Start polling immediately when page loads
    console.log("Page loaded, starting progress updates");
    updateProgress();
</script>
{% endblock %}
