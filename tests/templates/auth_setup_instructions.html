{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2>Authentication Setup Instructions</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p>You need to set up your Akamai API credentials to use this application.</p>
                    </div>

                    <h3>How to Set Up Your Akamai API Credentials</h3>

                    <ol>
                        <li>
                            <strong>Log in to the Akamai Control Center</strong>
                            <p>Visit <a href="https://control.akamai.com" target="_blank">control.akamai.com</a> and log
                                in with your Akamai account.</p>
                        </li>
                        <li>
                            <strong>Create API Credentials</strong>
                            <p>Either request your administrator to create an API client for you or create one yourself
                                (if you have the necessary permissions). Navigate to "Identity & Access" --> "API
                                Clients" --> "API Credentials" and click "Create API Client".</p>
                        </li>
                        <li>
                            <strong>Configure API Access</strong>
                            <p>Give your API client a name and description, then select the appropriate permissions
                                (Account switching must be enabled for OCD Customers[except Schneider Electric]).</p>
                        </li>
                        <li>
                            <strong>Save Your Credentials</strong>
                            <p>After creating the API client, download the edgerc file in your home directory
                                (C:\Users\<USER>\<.edgerc filename>) and save it in a secure location.</p>
                        </li>
                    </ol>

                    <h3>Using Your Credentials</h3>
                    <p>Once you have stored your API credentials, you can log in to the application:</p>
                    <div class="text-center mt-4">
                        <a class="btn btn-primary btn-lg" href="{% url 'auditor:login' %}">Go to Login Page</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}