{% extends "base.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h2>{{ title }}</h2>
        </div>
        <div class="card-body">
            <p>You are about to {% if audit.action_required %}disable{% else %}enable{% endif %} the action required
                status for:</p>
            <p class="font-weight-bold">{{ audit.ticket }} - {{ audit.customer_name }}</p>

            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="comment">Comment (optional):</label>
                    <textarea class="form-control" id="comment" name="comment" placeholder="Please explain why you're changing this status"
                              rows="3"></textarea>
                    <small class="form-text text-muted">Your username and the current time will be recorded with this
                        change.</small>
                </div>

                <input name="next" type="hidden" value="{{ next }}">

                <div class="form-group mt-4">
                    <button class="btn btn-primary" type="submit">Confirm</button>
                    <a class="btn btn-secondary" href="{{ next }}">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}