{% extends "base.html" %}
{% load static %}

{% block title %}Authentication Successful{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h2 class="mb-0"><i class="fas fa-check-circle me-2"></i>Authentication Successful</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h4 class="alert-heading">Welcome, {{ user.first_name }}!</h4>
                        <p>You have successfully authenticated to the WebGuardian Report Audit Assistant.</p>
                    </div>
                    
                    <h5 class="mt-4 mb-3">Your Account Information:</h5>
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th scope="row" style="width: 30%;">Username</th>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <th scope="row">Full Name</th>
                                <td>{{ user.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th scope="row">Email</th>
                                <td>{{ user.email }}</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="d-grid gap-2 mt-4">
                        <a href="{% url 'auditor:upload_file' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>Continue to Audit Application
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}