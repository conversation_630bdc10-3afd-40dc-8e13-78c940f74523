# Generated by Django 5.1.6 on 2025-04-14 13:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auditor', '0005_auditresults_action_history_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='auditresults',
            name='action_history',
            field=models.JSONField(blank=True, default=list, help_text='History of changes to action_required status', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='action_required',
            field=models.BooleanField(default=False, help_text='Flag indicating action is required due to issues'),
        ),
    ]
