from unittest.mock import patch, MagicMock

from django.test import TestCase, Client
from django.urls import reverse

from .auth import AkamaiAuthBackend


class AkamaiAuthBackendTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.login_url = reverse('auditor:login')
        self.backend = AkamaiAuthBackend()

    @patch('auditor.auth.open_akamai_session')
    def test_successful_authentication(self, mock_open_session):
        # Mock the session and responses
        mock_session = MagicMock()
        mock_session.get.side_effect = [
            # First call for self_url
            MagicMock(status_code=200),
            # Second call for profile_url
            MagicMock(status_code=200, json=lambda: {
                'firstName': 'Test',
                'lastName': 'User',
                'email': '<EMAIL>'
            })
        ]
        mock_open_session.return_value = (mock_session, '', '', '', '')

        # Test direct authentication
        user = self.backend.authenticate(None, username='testuser', password='testpass')
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertEqual(user.email, '<EMAIL>')

        # Test login view
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'testpass'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after successful login

    @patch('auditor.auth.open_akamai_session')
    def test_failed_authentication(self, mock_open_session):
        # Mock the session and responses
        mock_session = MagicMock()
        mock_session.get.return_value = MagicMock(status_code=401)  # Unauthorized
        mock_open_session.return_value = (mock_session, '', '', '', '')

        # Test direct authentication
        user = self.backend.authenticate(None, username='testuser', password='wrongpass')
        self.assertIsNone(user)

        # Test login view
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'wrongpass'
        })
        self.assertEqual(response.status_code, 401)  # Unauthorized status
