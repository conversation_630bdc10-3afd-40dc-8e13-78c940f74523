import pymupdf
import json
import re
import ast
from django.utils import timezone
import ollama
from loguru import logger
from json_repair import repair_json
from .models import AuditResults
import os
from datetime import datetime
import threading
import time
from functools import lru_cache
from django.core.cache import cache
from statistics import mean, median
from htmxtests.views import populate_data_from_api
from pydantic import BaseModel

from .variables import REPORT_NAME_MAPPING

# Global variables for tracking processing status
_processing_status = {}
_status_lock = threading.Lock()

# Global variables to track processing times
_processing_times = {}
_processing_times_lock = threading.Lock()

# Add to global variables at the top
_error_tickets = {}
_error_tickets_lock = threading.Lock()

# Add this at the top of the file with other global variables
# Initialize the processed clients set - make sure this is at module level
_processed_clients = set()
_processed_clients_lock = threading.Lock()

# Define required keys for normalization
required_keys = [
    "ticket", "date", "customer_name", "event_type", "priority",
    "destination_hostnames", "remediation", "remediation_action",
    "remediation_reason", "blacklisted_ips", "destination_hostnames",
    "wsa_link", "recommendations", "missing_values"
]

# Pre-compiled regex patterns for performance optimization
orange_ref_pattern = re.compile(r'(?i)Orange reference\s+(\w+)')
date_incident_pattern = re.compile(r'(?i)Date Incident\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC)')
priority_pattern = re.compile(r'\bPriority\s+(P[1-4])\b')
customer_pattern = re.compile(r'(?i)Customer\s+name\s*[:]\s*([A-Za-z0-9\s]+)')
event_type_pattern = re.compile(r'IEX_DOS\.\d+-WGA\d+\s*-\s*.*?(?=\bPriority\b|$)', re.IGNORECASE | re.DOTALL)
service_type_pattern = re.compile(r'Service\s+Type\s*\n?\s*(DDos[^\n\r]*)', re.IGNORECASE)
akamai_link_pattern = re.compile(r'https?://(?:[\w-]+\.)+akamai\.com/[\w\-./]+')

# LLM client setup
client = ollama.Client()
#model = "WebGuardian_Report_Analyzer_llama32_1B"
model = "WebGuardian_Report_Analyzer_llama32_3B"

# Define the schema for the structured output
class ResponseFormat(BaseModel):
    remediation: bool
    remediation_action: str
    remediation_reason: str
    blacklisted_ips: str | None = None  # Make field optional with default None
    destination_hostnames: str | None = None  # Make field optional with default None
    recommendations: str | None = None  # Make field optional with default None

def extract_pdf_text(filepath):
    """
    Extract text content from a PDF file.

    Args:
        filepath (str): Path to the PDF file

    Returns:
        str: Extracted text content from the PDF, or empty string if extraction fails
    """
    text = ""
    start_time = time.time()

    try:
        # Check if file exists and is accessible
        if not os.path.isfile(filepath):
            logger.error(f"PDF file not found: {filepath}")
            return text

        # Open PDF with proper error handling
        with pymupdf.open(filepath) as pdf_document:
            # Pre-allocate text buffer for better performance
            page_texts = []

            # Process pages
            for page_num in range(len(pdf_document)):
                try:
                    page = pdf_document.load_page(page_num)
                    page_texts.append(page.get_text("text"))
                except Exception as e:
                    logger.error(f"Error extracting text from page {page_num} in {filepath}: {e}")

            # Join all page texts
            text = "".join(page_texts)

        logger.info(f"PDF extraction completed in {time.time() - start_time:.2f}s: {filepath}")
    except pymupdf.fitz.FileDataError:
        logger.error(f"Invalid or corrupted PDF file: {filepath}")
    except PermissionError:
        logger.error(f"Permission denied when accessing PDF file: {filepath}")
    except Exception as e:
        logger.error(f"Error reading PDF {filepath}: {str(e)}")

    return text

@lru_cache(maxsize=128)
def parse_ticketid(text):
    """
    Parse ticket ID from text with caching for performance.

    Args:
        text (str): Text to parse

    Returns:
        str or None: Extracted ticket ID or None if not found
    """
    if not text:
        return None

    match = orange_ref_pattern.search(text)
    return match.group(1) if match else None

@lru_cache(maxsize=128)
def parse_datetime(text):
    """
    Parse datetime from text with multiple format support and caching.

    Args:
        text (str): Text to parse

    Returns:
        datetime or None: Parsed datetime object or None if not found
    """
    if not text:
        logger.warning("Empty text provided to parse_datetime")
        return None

    # First try the specific pattern with text after UTC
    specific_pattern = r'(?i)Date Incident\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC).*?'
    match = re.search(specific_pattern, text)
    if match:
        datetime_str = match.group(1)
        logger.debug(f"Found date string with specific pattern: {datetime_str}")
        try:
            # Use Python's datetime with timezone info instead of Django's timezone
            from datetime import datetime, timezone as py_timezone
            naive_datetime = datetime.strptime(datetime_str.replace(" UTC", ""), '%Y-%m-%d %H:%M:%S')
            aware_datetime = naive_datetime.replace(tzinfo=py_timezone.utc)
            logger.info(f"Successfully parsed date: {aware_datetime}")
            return aware_datetime
        except Exception as e:
            logger.error(f"Error parsing specific datetime format: {str(e)}")

    # Define date patterns to try
    date_patterns = [
        # Patterns with text after UTC
        r'(?i)Date Incident\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC).*',
        r'(?i)Date\s+of\s+Incident\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC).*',
        r'(?i)Incident\s+Date\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC).*',
        r'(?i)Date\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC).*',

        # Original patterns
        r'(?i)Date Incident\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Date\s+of\s+Incident\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Incident\s+Date\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Date\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Date\s+Incident\s+(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Date\s+of\s+Incident\s*[:]\s*(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Incident\s+Date\s*[:]\s*(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2} UTC)',
        r'(?i)Date\s*[:]\s*(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2} UTC)',

        # Additional patterns without UTC
        r'(?i)Date Incident\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',
        r'(?i)Date\s+of\s+Incident\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',
        r'(?i)Incident\s+Date\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',
        r'(?i)Date\s*[:]\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',
    ]

    # Log a sample of the text for debugging
    logger.debug(f"Parsing date from text sample: {text[:200]}...")

    # Import Python's timezone module
    from datetime import timezone as py_timezone

    for pattern in date_patterns:
        match = re.search(pattern, text)
        if match:
            datetime_str = match.group(1)
            logger.debug(f"Found date string: {datetime_str} with pattern: {pattern}")

            try:
                # Handle UTC suffix
                has_utc = "UTC" in datetime_str
                clean_datetime_str = datetime_str.replace(" UTC", "")

                # Try parsing with different formats
                formats_to_try = [
                    '%Y-%m-%d %H:%M:%S',     # 2023-04-01 12:30:45
                    '%Y/%m/%d %H:%M:%S',     # 2023/04/01 12:30:45
                    '%d-%m-%Y %H:%M:%S',     # 01-04-2023 12:30:45
                    '%d/%m/%Y %H:%M:%S',     # 01/04/2023 12:30:45
                ]

                for fmt in formats_to_try:
                    try:
                        naive_datetime = datetime.strptime(clean_datetime_str, fmt)
                        # Add UTC timezone if it was in the original string
                        aware_datetime = naive_datetime.replace(tzinfo=py_timezone.utc if has_utc else None)
                        logger.info(f"Successfully parsed date: {aware_datetime} using format: {fmt}")
                        return aware_datetime
                    except ValueError:
                        continue

                logger.warning(f"Failed to parse date string: {datetime_str} with any format")
            except Exception as e:
                logger.error(f"Error parsing datetime: {str(e)}")

    # If we get here, no valid date was found
    logger.warning("No valid date format found in text.")
    return None

@lru_cache(maxsize=128)
def parse_priority(text):
    """
    Parse priority from text with caching.

    Args:
        text (str): Text to parse

    Returns:
        str or None: Extracted priority or None if not found
    """
    if not text:
        return None

    match = priority_pattern.search(text)
    return match.group(1) if match else None

@lru_cache(maxsize=128)
def parse_customer_name(text):
    """
    Parse customer name from text with caching.

    Args:
        text (str): Text to parse

    Returns:
        str or None: Extracted customer name or None if not found
    """
    if not text:
        return None

    pattern = r'Customer\s+(.*?)\s+Orange Reference'
    match = re.search(pattern, text, re.IGNORECASE)
    return match.group(1).strip() if match else None

@lru_cache(maxsize=128)
def parse_event_type(text, filepath=None):
    """
    Parse event type from text with caching and fallback to filename.

    Args:
        text (str): Text to parse
        filepath (str, optional): Path to the file for fallback extraction

    Returns:
        str or None: Extracted event type or None if not found
    """
    if not text:
        logger.warning("Empty text provided to parse_event_type")
        return None

    # Try IEX_DOS format first from text content
    match = event_type_pattern.search(text)
    if match:
        # Clean up the extracted text
        event_type = match.group(0)
        event_type = re.sub(r'\s+', ' ', event_type)  # Replace multiple spaces with single space
        event_type = event_type.strip()  # Remove leading/trailing whitespace

        # Check if the match is too long (> 65 characters)
        if len(event_type) <= 65:
            logger.info(f"Successfully extracted event type from text: {event_type}")
            return event_type
        else:
            logger.warning(f"Extracted event type too long ({len(event_type)} chars), falling back to filename extraction")

    # Fallback: Extract from filename if filepath is provided
    if filepath:
        try:
            filename = os.path.basename(filepath)
            # Extract IEX_DOS pattern from filename
            filename_pattern = re.compile(r'(IEX_DOS\.\d+-WGA\d+[_-].*?)\.pdf$', re.IGNORECASE)
            match = filename_pattern.search(filename)

            if match:
                event_type = match.group(1)
                # Replace underscores with spaces for better readability
                event_type = event_type.replace('_', ' ')
                logger.info(f"Extracted event type from filename: {event_type}")
                return event_type

            # Alternative pattern if the first one doesn't match
            alt_pattern = re.compile(r'-([^-]*IEX_DOS[^-]*)-', re.IGNORECASE)
            match = alt_pattern.search(filename)
            if match:
                event_type = match.group(1)
                event_type = event_type.replace('_', ' ')
                logger.info(f"Extracted event type from filename using alternative pattern: {event_type}")
                return event_type

        except Exception as e:
            logger.error(f"Error extracting event type from filename: {str(e)}")

    logger.warning("Failed to extract event type from both text and filename")
    return None

def parse_text(text):
    """
    Clean and parse text by removing confidential markers.

    Args:
        text (str): Text to clean

    Returns:
        str: Cleaned text
    """
    if not text:
        return ""

    # Remove confidentiality markers and trim
    text = text.replace("Orange Confidential", "").replace("Orange Restricted", "").strip()

    # Truncate at "Information needed" section if present
    if "Information needed" in text:
        text = text.split("Information needed")[0]

    return text

def extract_akamai_links(filepath):
    """
    Extract Akamai control panel links from PDF file.

    Args:
        filepath (str): Path to the PDF file

    Returns:
        list: List of Akamai control panel links found, or empty list if none
    """
    akamai_links = []
    try:
        # Open the PDF document
        doc = pymupdf.open(filepath)

        # Iterate through all pages to find links
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            links = page.links()

            # Check each link for Akamai control panel domain
            if links:
                for link in links:
                    # Extract URI if present
                    if 'uri' in link and link['uri']:
                        if link['uri'].startswith("https://control.akamai.com"):
                            akamai_links.append(link['uri'])

        # If no links found in annotations, fall back to text search
        if not akamai_links:
            doc.close()
            text = extract_pdf_text(filepath)
            # Find all links that start with https://control.akamai.com
            control_pattern = re.compile(r'https://control\.akamai\.com[^\s"\'<>]+')
            matches = control_pattern.findall(text)
            akamai_links = matches if matches else []
        else:
            doc.close()

        return akamai_links

    except Exception as e:
        logger.error(f"Error extracting Akamai links from {filepath}: {str(e)}")
        return []



def normalize_keys(input_data, required_keys=required_keys):
    """
    Normalize dictionary keys to a consistent format.

    Args:
        input_data (dict or str): Input data to normalize
        required_keys (list): List of required keys

    Returns:
        str: JSON string with normalized keys

    Raises:
        ValueError: If input data is not a dictionary or JSON string
    """
    try:
        if isinstance(input_data, str):
            input_data = json.loads(input_data)
        elif not isinstance(input_data, dict):
            raise ValueError("Input data must be a dictionary or JSON string")

        # Normalize keys
        normalized_required_keys = [key.lower().replace(" ", "_") for key in required_keys]
        normalized_dict = {
            key.lower().replace(" ", "_"): value
            for key, value in input_data.items()
            if key.lower().replace(" ", "_") in normalized_required_keys
        }

        return json.dumps(normalized_dict, indent=4)
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error in normalize_keys: {str(e)}")
        raise ValueError(f"Invalid JSON string: {str(e)}")
    except Exception as e:
        logger.error(f"Error in normalize_keys: {str(e)}")
        raise

def get_blacklisted_ips(df):
    """
    Extract blacklisted IPs from DataFrame.

    Args:
        df (DataFrame): Pandas DataFrame containing audit data

    Returns:
        dict: Dictionary mapping ticket IDs to lists of blacklisted IPs
    """
    result = {}

    try:
        if df is None or df.empty:
            logger.warning("Empty DataFrame provided to get_blacklisted_ips")
            return result

        for _, row in df.iterrows():
            if row.get('remediation') is True and row.get('blacklisted_ips'):
                ip_addresses = []

                # Handle different formats of blacklisted_ips
                if isinstance(row['blacklisted_ips'], str) and row['blacklisted_ips'].startswith("["):
                    try:
                        ip_addresses = ast.literal_eval(row['blacklisted_ips'])
                    except (ValueError, SyntaxError) as e:
                        logger.error(f"Error parsing IP list for ticket {row.get('ticket', 'unknown')}: {str(e)}")
                elif isinstance(row['blacklisted_ips'], list):
                    ip_addresses = row['blacklisted_ips']
                elif isinstance(row['blacklisted_ips'], str):
                    ip_addresses = [ip.strip() for ip in row['blacklisted_ips'].split(",")]

                if ip_addresses and row.get('ticket'):
                    result[row['ticket']] = ip_addresses
    except Exception as e:
        logger.error(f"Error in get_blacklisted_ips: {str(e)}")

    return result

def fix_json_string(input_str):
    """
    Fix common JSON string formatting issues.

    Args:
        input_str (str): JSON string to fix

    Returns:
        str or None: Fixed JSON string or None if unfixable
    """
    if not input_str:
        return None

    try:
        # Fix common formatting issues
        json_str = (input_str.replace('{ ', '{')
                            .replace(' {', '{')
                            .replace('} ', '}')
                            .replace(' }', '}')
                            .replace('\\', '')
                            .strip()
                            .strip('"'))

        # Try to parse the fixed JSON
        data = json.loads(json_str)
        return json.dumps(data, indent=4)
    except json.JSONDecodeError as e:
        logger.error(f"JSONDecodeError after fixes: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error in fix_json_string: {str(e)}")
        return None

def generate_timestamp():
    """
    Generate a timestamp string for filenames.

    Returns:
        str: Timestamp in format YYYYMMDD_HHMMSS
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def process_file(filepath, stop_event=None):
    """
    Process a single PDF file.

    Args:
        filepath (str): Path to the PDF file
        stop_event (threading.Event, optional): Event to signal stopping. Defaults to None.
    """
    global _processed_clients
    start_time = time.time()
    filename = os.path.basename(filepath)
    ticketid = "Unknown"  # Initialize ticketid

    try:
        # Check for stop event
        if stop_event and stop_event.is_set():
            logger.info(f"Stopping processing of {filepath}")
            return

        # Extract and parse text
        logger.info(f"Processing file: {filepath}")
        text = extract_pdf_text(filepath)
        if not text:
            logger.error(f"Failed to extract text from {filepath}")
            with _error_tickets_lock:
                _error_tickets[filepath] = "Unknown (text extraction failed)"
            return

        wsa_link = extract_akamai_links(filepath)
        parsed_text = parse_text(text)

        # Extract metadata
        client_name = parse_customer_name(parsed_text)
        print(f"DEBUG: Extracted client_name: '{client_name}'")
        logger.info(f"Extracted client_name: '{client_name}'")

        ticketid = parse_ticketid(parsed_text)
        ticket_date = parse_datetime(parsed_text)
        ticket_priority = parse_priority(parsed_text)
        # Pass filepath to parse_event_type for fallback extraction
        ticket_event_type = parse_event_type(text, filepath)

        # Validate required metadata
        if not ticketid:
            logger.error(f"No ticket ID found in {filepath}")
            with _error_tickets_lock:
                _error_tickets[filepath] = "Unknown (no ticket ID found)"
            return

        # If client name is detected and hasn't been processed before, populate blacklist items
        if client_name:
            print(f"DEBUG: Client name detected: {client_name}")
            logger.info(f"Client name detected: {client_name}")

            with _processed_clients_lock:
                print(f"DEBUG: Current processed clients: {_processed_clients}")
                logger.info(f"Current processed clients: {_processed_clients}")

                if client_name not in _processed_clients:
                    print(f"DEBUG: Client {client_name} not in processed clients, will populate blacklist")
                    logger.info(f"Client {client_name} not in processed clients, will populate blacklist")

                    try:
                        # Use relative import
                        from . import verification
                        logger.info(f"First encounter with client {client_name}, populating blacklist items")
                        result = verification.populate_blacklist_items(client_name)
                        logger.info(f"Blacklist population result: {result['status']} - {result['message']}")
                        # Add to processed clients set after successful processing
                        _processed_clients.add(client_name)
                    except Exception as e:
                        logger.error(f"Error populating blacklist items for {client_name}: {str(e)}")
                else:
                    print(f"DEBUG: Client {client_name} already processed, skipping")
                    logger.info(f"Client {client_name} already processed, skipping")
        else:
            print(f"DEBUG: No client name detected for {filepath}")
            logger.info(f"No client name detected for {filepath}")

        # Generate LLM prompt and get response
        prompt = "REPORTINPUT : " + parsed_text
        logger.info(f"Making request to {model} for ticket {ticketid}")

        try:
            response = client.generate(model=model, prompt=prompt)
            response_string = response.response

            # Try to parse JSON with multiple methods
            resp_dict = None

            # Method 1: Pydantic validation (try first)
            try:
                logger.info(f"Attempting Pydantic validation for {filepath}")
                structured_response = ResponseFormat.model_validate_json(response_string)
                resp_dict = structured_response.model_dump()
                logger.success(f"Successfully validated and parsed JSON using Pydantic")
            except Exception as pydantic_error:
                logger.info(f"Pydantic validation failed: {str(pydantic_error)}, trying other methods")



                # Method 2: JSON-repair
                try:
                    good_json_string = repair_json(response_string)
                    resp_dict = json.loads(normalize_keys(good_json_string))
                    logger.success(f"Parsed JSON using JSON-repair")
                except json.JSONDecodeError:
                    logger.error(f"JSON-repair parsing failed, trying fix_json_string")

                    # Method 3: fix_json_string
                    try:
                        rs = fix_json_string(response_string)
                        resp_dict = json.loads(normalize_keys(rs))
                        logger.success(f"Parsed JSON using fix_json_string")
                    except (json.JSONDecodeError, TypeError):
                        logger.info(f"Attempting direct JSON parsing for {filepath}")

                        # Method 4: Direct parsing
                        try:
                            resp_dict = json.loads(normalize_keys(response_string))
                            logger.success(f"Parsed JSON directly from {model} response")
                        except json.JSONDecodeError:
                            logger.error(f"Direct JSON parsing failed")
                            logger.error(f"All JSON parsing methods failed for {filepath}")

            logger.debug(f"Response from {model}: {response_string[:100]}...")

            # Process successful parsing
            if resp_dict:
                # Add metadata to response
                resp_dict["ticket"] = ticketid
                resp_dict["ticket_date"] = ticket_date
                resp_dict["event_type"] = ticket_event_type
                resp_dict["priority"] = ticket_priority
                resp_dict["customer_name"] = client_name
                resp_dict["wsa_link"] = wsa_link

                # Save to database
                try:
                    report, created = AuditResults.objects.update_or_create(
                        ticket=resp_dict["ticket"],
                        defaults=resp_dict
                    )

                    if created:
                        logger.info(f"Created new report with ticket ID {resp_dict['ticket']}")
                    else:
                        logger.info(f"Updated existing report with ticket ID {resp_dict['ticket']}")
                except Exception as db_error:
                    logger.error(f"Database error for ticket {ticketid}: {str(db_error)}")
            else:
                logger.error(f"Unable to process response for {filename}")
                logger.debug(f"Raw response: {response_string[:200]}...")
        except Exception as llm_error:
            logger.error(f"LLM processing error for {filepath}: {str(llm_error)}")
            with _error_tickets_lock:
                _error_tickets[filepath] = ticketid

    except Exception as e:
        logger.error(f"Error processing file {filepath}: {str(e)}")
        # Get ticket ID if available, but don't overwrite if we already have it
        if ticketid == "Unknown":
            try:
                ticketid = parse_ticketid(parse_text(extract_pdf_text(filepath))) or "Unknown"
            except:
                ticketid = "Unknown"

        with _error_tickets_lock:
            _error_tickets[filepath] = ticketid
    finally:
        processing_time = time.time() - start_time
        logger.info(f"Processed {filepath} in {processing_time:.2f} seconds")

        # Store processing time
        with _processing_times_lock:
            _processing_times[filepath] = processing_time

def view_running_threads():
    """
    Get a list of all running threads.

    Returns:
        list: List of running thread objects
    """
    return threading.enumerate()

def stop_all_threads():
    """
    Signal all processing threads to stop.
    """
    logger.info("Signaling all processing threads to stop")
    stop_event.set()

def get_audit_progress(directory_path):
    """
    Get the current processing status for a directory

    Args:
        directory_path (str): Path to the directory being processed

    Returns:
        dict: Status information including percentage, status message, and completion flag
    """
    with _status_lock:
        status_data = _processing_status.get(directory_path, {
            'percentage': 0,
            'status': 'Initializing...',
            'complete': False,
            'processed_files': 0,
            'total_files': 0
        })

        # Create a copy to avoid reference issues
        return status_data.copy()

def _update_processing_status(directory_path, completed, total, status=None, complete=False):
    """
    Update the processing status for a directory

    Args:
        directory_path (str): Path to the directory being processed
        completed (int): Number of files processed
        total (int): Total number of files to process
        status (str, optional): Custom status message. Defaults to None.
        complete (bool, optional): Whether processing is complete. Defaults to False.
    """
    percentage = min(int((completed / total) * 100), 100) if total > 0 else 0

    if not status:
        status = f"Processing {completed}/{total} files ({percentage}%)"

    if complete:
        status = "Processing complete" if not status.startswith("Error") else status
        percentage = 100

    logger.info(f"Updating progress: {completed}/{total} files ({percentage}%) - {status}")

    with _status_lock:
        _processing_status[directory_path] = {
            'percentage': percentage,
            'status': status,
            'complete': complete or percentage >= 100,
            'processed_files': completed,
            'total_files': total
        }

def get_file_list(directory_path):
    """
    Get a list of PDF files in the directory

    Args:
        directory_path (str): Path to the directory

    Returns:
        list: List of PDF file paths
    """
    pdf_files = []
    try:
        for root, _, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        logger.info(f"Found {len(pdf_files)} PDF files in {directory_path}")
        return pdf_files
    except Exception as e:
        logger.error(f"Error getting file list from {directory_path}: {str(e)}")
        return []

def process_directory(directory_path):
    """
    Process all PDF files in a directory.

    Args:
        directory_path (str): Path to the directory containing PDF files

    Returns:
        bool: True if processing was successful, False otherwise
    """
    global _processed_clients

    # Reset processed clients for this batch
    with _processed_clients_lock:
        _processed_clients.clear()
        print(f"DEBUG: Reset processed clients set at start of directory processing")
        logger.info(f"Reset processed clients set at start of directory processing")

    try:
        # Reset stop event
        global stop_event
        stop_event = threading.Event()

        # Reset processing times for this batch
        with _processing_times_lock:
            _processing_times.clear()

        # Reset error tracking
        with _error_tickets_lock:
            _error_tickets.clear()

        # Initialize progress tracking
        file_list = get_file_list(directory_path)
        total_files = len(file_list)
        processed_files = 0

        # Create a progress tracking record
        _update_processing_status(directory_path, processed_files, total_files)

        # Process each file
        for file_path in file_list:
            try:
                # Check if processing should stop
                if stop_event.is_set():
                    logger.info("Processing stopped by user request")
                    _update_processing_status(directory_path, processed_files, total_files,
                                             status="Processing stopped by user", complete=True)
                    return False

                # Process the file
                process_file(file_path, stop_event)

                # Update progress
                processed_files += 1
                _update_processing_status(directory_path, processed_files, total_files)

            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")
                # Continue with next file even if one fails

        # Log error summary if any errors occurred
        with _error_tickets_lock:
            error_count = len(_error_tickets)
            if error_count > 0:
                # Create a list of failed ticket IDs for the summary
                failed_tickets = list(_error_tickets.values())
                failed_tickets_str = ", ".join(failed_tickets)

                # Log error summary header
                logger.error(f"\n===== ERROR SUMMARY =====")
                logger.error(f"Processing completed with {error_count} errors out of {total_files} files")
                logger.error(f"Failed ticket IDs: {failed_tickets_str}")
                logger.error(f"The following files had errors:")

                # Print to console as well for visibility
                print(f"\n===== ERROR SUMMARY =====")
                print(f"Processing completed with {error_count} errors out of {total_files} files")
                print(f"Failed ticket IDs: {failed_tickets_str}")
                print(f"The following files had errors:")

                # List each error with file and ticket ID
                for filepath, ticketid in _error_tickets.items():
                    error_msg = f"  - {os.path.basename(filepath)}: Ticket {ticketid}"
                    logger.error(error_msg)
                    print(error_msg)

                logger.error(f"========================")
                print(f"========================\n")

                # Update status to indicate errors with ticket IDs
                _update_processing_status(
                    directory_path,
                    processed_files,
                    total_files,
                    status=f"Completed with {error_count} errors. Failed tickets: {failed_tickets_str}",
                    complete=True
                )
            else:
                # Mark processing as complete
                _update_processing_status(directory_path, processed_files, total_files, complete=True)

        # Log processing statistics
        log_processing_statistics()

        # Run structure check after processing is complete
        from .structure_check import check_directory_for_fields_and_languages, required_fields, excluded_sentences
        logger.info(f"Starting structure check for directory: {directory_path}")
        check_directory_for_fields_and_languages(directory_path, required_fields, excluded_sentences)

        # Return success based on whether there were errors
        with _error_tickets_lock:
            success = len(_error_tickets) == 0
            if not success:
                logger.warning(f"Directory processing considered UNSUCCESSFUL due to {len(_error_tickets)} errors")
                print(f"Directory processing considered UNSUCCESSFUL due to {len(_error_tickets)} errors")
            else:
                logger.info(f"Directory processing completed SUCCESSFULLY with no errors")
                print(f"Directory processing completed SUCCESSFULLY with no errors")
            return success

    except Exception as e:
        logger.error(f"Error processing directory {directory_path}: {str(e)}")
        # Mark as complete but with error
        _update_processing_status(directory_path, 0, 0, status=f"Error: {str(e)}", complete=True)
        logger.error(f"Directory processing FAILED due to exception: {str(e)}")
        print(f"Directory processing FAILED due to exception: {str(e)}")
        return False

def get_audit_results(start_date=None, end_date=None, search_term=None, priorities=None, customers=None):
    """
    Get audit results with optional filtering

    Args:
        start_date (str): Start date for filtering (YYYY-MM-DD)
        end_date (str): End date for filtering (YYYY-MM-DD)
        search_term (str): Text to search for in results
        priorities (list): List of priorities to filter by
        customers (list): List of customers to filter by

    Returns:
        tuple: (results, unique_priorities, unique_customers)
    """
    try:
        from .models import AuditResults

        # Start with all results
        queryset = AuditResults.objects.all().order_by('-date')

        # Apply date filters if provided
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(date__gte=start_date)
            except ValueError:
                logger.warning(f"Invalid start date format: {start_date}")

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(date__lte=end_date)
            except ValueError:
                logger.warning(f"Invalid end date format: {end_date}")

        # Apply text search if provided
        if search_term:
            queryset = queryset.filter(
                Q(ticket__icontains=search_term) |
                Q(customer__icontains=search_term) |
                Q(event_type__icontains=search_term) |
                Q(notes__icontains=search_term)
            )

        # Apply priority filter if provided
        if priorities:
            queryset = queryset.filter(priority__in=priorities)

        # Apply customer filter if provided
        if customers:
            queryset = queryset.filter(customer__in=customers)

        # Get unique values for filter dropdowns
        all_results = AuditResults.objects.all()
        unique_priorities = sorted(list(set(all_results.values_list('priority', flat=True).distinct())))
        unique_customers = sorted(list(set(all_results.values_list('customer', flat=True).distinct())))

        # Remove None values
        unique_priorities = [p for p in unique_priorities if p]
        unique_customers = [c for c in unique_customers if c]

        return list(queryset), unique_priorities, unique_customers
    except Exception as e:
        logger.error(f"Error retrieving audit results: {str(e)}")
        return [], [], []

def log_processing_statistics():
    """
    Calculate and log statistics about file processing times.

    Returns:
        dict: Dictionary containing processing time statistics
    """
    with _processing_times_lock:
        if not _processing_times:
            logger.info("No files were processed, no statistics available")
            return {
                "total_files": 0,
                "total_time": 0,
                "average_time": 0,
                "median_time": 0,
                "min_time": 0,
                "max_time": 0
            }

        times = list(_processing_times.values())
        total_time = sum(times)
        avg_time = mean(times)
        med_time = median(times)
        min_time = min(times)
        max_time = max(times)

        # Find slowest and fastest files
        slowest_file = max(_processing_times.items(), key=lambda x: x[1])
        fastest_file = min(_processing_times.items(), key=lambda x: x[1])

        stats = {
            "total_files": len(times),
            "total_time": total_time,
            "average_time": avg_time,
            "median_time": med_time,
            "min_time": min_time,
            "max_time": max_time,
            "slowest_file": os.path.basename(slowest_file[0]),
            "slowest_time": slowest_file[1],
            "fastest_file": os.path.basename(fastest_file[0]),
            "fastest_time": fastest_file[1]
        }

        # Log the statistics
        logger.info("=== PDF Processing Statistics ===")
        logger.info(f"Total files processed: {stats['total_files']}")
        logger.info(f"Total processing time: {stats['total_time']:.2f} seconds")
        logger.info(f"Average processing time: {stats['average_time']:.2f} seconds per file")
        logger.info(f"Median processing time: {stats['median_time']:.2f} seconds")
        logger.info(f"Fastest processing time: {stats['min_time']:.2f} seconds ({stats['fastest_file']})")
        logger.info(f"Slowest processing time: {stats['max_time']:.2f} seconds ({stats['slowest_file']})")
        logger.info("===============================")

        # Also print to console
        print("\n=== PDF Processing Statistics ===")
        print(f"Total files processed: {stats['total_files']}")
        print(f"Total processing time: {stats['total_time']:.2f} seconds")
        print(f"Average processing time: {stats['average_time']:.2f} seconds per file")
        print(f"Median processing time: {stats['median_time']:.2f} seconds")
        print(f"Fastest processing time: {stats['min_time']:.2f} seconds ({stats['fastest_file']})")
        print(f"Slowest processing time: {stats['max_time']:.2f} seconds ({stats['slowest_file']})")
        print("===============================\n")

        return stats

def get_client_details(client_name):
    """
    Get client details from REPORT_NAME_MAPPING.

    Args:
        client_name (str): Name of the client

    Returns:
        tuple: (tenant, blacklist, switch_key)

    Raises:
        KeyError: If client_name not found in REPORT_NAME_MAPPING
    """
    try:
        tenant = REPORT_NAME_MAPPING[client_name]["akamai_tenant"]
        blacklist = REPORT_NAME_MAPPING[client_name]["blacklist"]
        switch_key = REPORT_NAME_MAPPING[client_name]["switch_key"]
        return tenant, blacklist, switch_key
    except KeyError as e:
        logger.error(f"Client '{client_name}' not found in REPORT_NAME_MAPPING: {e}")
        raise KeyError(f"Client '{client_name}' not found in configuration") from e

def update_blacklist_comments(audit_result, ip, comment, max_length=10000):
    """
    Update blacklist comments for an AuditResults object.

    Args:
        audit_result: AuditResults object to update
        ip (str): IP address the comment is about
        comment (str): Comment about blacklist status
        max_length (int): Maximum length for comments before truncating

    Returns:
        bool: True if comments were updated, False otherwise
    """
    # Skip if IP already has comments
    if audit_result.blacklist_comments:
        # Extract IPs from existing comments
        already_commented = set(re.findall(r"\b(?:\d{1,3}\.){3}\d{1,3}\b", audit_result.blacklist_comments))
        if ip in already_commented:
            logger.info(f"Skipping blacklist comment for already commented IP: {ip}")
            return False

    # Initialize comments if None
    if audit_result.blacklist_comments is None:
        audit_result.blacklist_comments = ""
        logger.debug("Initialized empty blacklist_comments")

    # Add timestamp to comment
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Append additional text to the comment
    additional_text = "According to the report, this IP was blacklisted. However, it is currently not present in the customer's blacklist. Please verify"
    formatted_comment = f"[{timestamp}] {comment} - {additional_text}"

    # Append or set blacklist comments
    if audit_result.blacklist_comments:
        # Check if we're approaching the max length
        if len(audit_result.blacklist_comments) + len(formatted_comment) + 1 > max_length:
            logger.warning("Blacklist comments approaching max length, truncating")
            audit_result.blacklist_comments = f"{audit_result.blacklist_comments[:max_length//2]}...\n[TRUNCATED]\n{formatted_comment}"
        else:
            audit_result.blacklist_comments += f"\n{formatted_comment}"
    else:
        audit_result.blacklist_comments = formatted_comment

    logger.info(f"Added blacklist comment for {ip}: {comment}")
    return True