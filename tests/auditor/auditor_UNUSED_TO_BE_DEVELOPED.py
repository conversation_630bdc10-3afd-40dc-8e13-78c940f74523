"""
PDF Report Auditor

This module processes PDF reports, extracts information, and analyzes them using an LLM model.
It monitors a directory for new PDF files, processes them, and outputs results to an Excel file.
"""

#
# # Configuration
# start_time = time.time()
# begin_time = datetime.now()
# logger.info(f"Start date and time: {begin_time}")
#
# # Directory containing PDF files
# subdirectory = "tests/staticfiles/report_analysis"
#
# # Required keys for data validation
# required_keys = [
#     "ticket", "date", "customer_name", "event_type", "priority",
#     "destination_hostnames", "remediation", "remediation_action",
#     "remediation_reason", "blacklisted_ips", "destination_hostnames",
#     "wsa_link", "recommendations", "missing_values"
# ]
#
# # Pre-compiled regex patterns for performance optimization
# ORANGE_REF_PATTERN = re.compile(r'(?i)Orange reference\s+(\w+)')
# DATE_INCIDENT_PATTERN = re.compile(r'(?i)Date Incident\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} UTC)')
# CUSTOMER_PATTERN = re.compile(r'Customer\s+(.*?)\s+Orange Reference', re.IGNORECASE)
# EVENT_TYPE_PATTERN = re.compile(r'IEX_DOS\.\d+\s*-\s*WGA\d+\s*-\s*.*?(?=\bPriority\b|$)', re.IGNORECASE | re.DOTALL)
# PRIORITY_PATTERN = re.compile(r'\bPriority\s+(P[1-4])\b')
#
# # Global variables
# df = pd.DataFrame(columns=required_keys)
# stop_event = threading.Event()
#
#
# def extract_pdf_text(filepath):
#     """
#     Extracts text from a PDF file using PyMuPDF (fitz).
#
#     Args:
#         filepath (str): Path to the PDF file
#
#     Returns:
#         str: Extracted text from the PDF
#
#     Raises:
#         Exception: If there's an error reading the PDF
#     """
#     text = ""
#     try:
#         pdf_document = fitz.open(filepath)
#         try:
#             for page_num in range(len(pdf_document)):
#                 page = pdf_document.load_page(page_num)
#                 text += page.get_text("text")  # Extract plain text
#         finally:
#             pdf_document.close()  # Ensure document is closed even if exception occurs
#     except Exception as e:
#         logger.error(f"Error reading PDF {filepath}: {e}")
#         raise
#     return text
#
#
# def parse_ticketid(text):
#     """
#     Extracts the ticket ID from the text.
#
#     Args:
#         text (str): Text to parse
#
#     Returns:
#         str or None: Extracted ticket ID or None if not found
#     """
#     match = ORANGE_REF_PATTERN.search(text)
#     return match.group(1) if match else None
#
#
# def parse_customer_name(text):
#     """
#     Extracts the customer name from the text.
#
#     Args:
#         text (str): Text to parse
#
#     Returns:
#         str or None: Extracted customer name or None if not found
#     """
#     match = CUSTOMER_PATTERN.search(text)
#     return match.group(1).strip() if match else None
#
#
# def parse_event_type(text):
#     """
#     Extracts the event type from the text.
#
#     Args:
#         text (str): Text to parse
#
#     Returns:
#         str or None: Extracted event type or None if not found
#     """
#     match = EVENT_TYPE_PATTERN.search(text)
#     return match.group(0).replace('\n', ' ').strip() if match else None

#
# def parse_datetime(text):
#     """
#     Extracts the date and time from the text.
#
#     Args:
#         text (str): Text to parse
#
#     Returns:
#         datetime or None: Parsed datetime object or None if not found
#     """
#     match = DATE_INCIDENT_PATTERN.search(text)
#     if match:
#         try:
#             datetime_str = match.group(1)
#             return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S %Z')
#         except ValueError as e:
#             logger.error(f"Error parsing datetime: {e}")
#             return None
#     return None
#
#
# def parse_priority(text):
#     """
#     Extracts the priority from the text.
#
#     Args:
#         text (str): Text to parse
#
#     Returns:
#         str or None: Extracted priority or None if not found
#     """
#     match = PRIORITY_PATTERN.search(text)
#     return match.group(1) if match else None
#
#
# def parse_text(text):
#     """
#     Cleans and formats text for easier parsing.
#
#     Args:
#         text (str): Text to clean
#
#     Returns:
#         str: Cleaned text
#     """
#     text = text.replace("Orange Confidential", "").replace("Orange Restricted", "").strip()
#     if "Information needed" in text:
#         text = text.split("Information needed")[0]
#     return text
#
#
# def normalize_keys(input_data, required_keys=required_keys):
#     """
#     Normalizes the keys of a dictionary or JSON string to match required keys.
#
#     Args:
#         input_data (dict or str): Dictionary or JSON string to normalize
#         required_keys (list): List of required keys
#
#     Returns:
#         str: JSON string with normalized keys
#
#     Raises:
#         ValueError: If input_data is not a dictionary or JSON string
#         json.JSONDecodeError: If input_data is a string but not valid JSON
#     """
#     try:
#         if isinstance(input_data, str):
#             input_data = json.loads(input_data)
#         elif not isinstance(input_data, dict):
#             raise ValueError("Input data must be a dictionary or JSON string")
#
#         normalized_required_keys = [key.lower().replace(" ", "_") for key in required_keys]
#         normalized_dict = {
#             key.lower().replace(" ", "_"): value
#             for key, value in input_data.items() if key.lower().replace(" ", "_") in normalized_required_keys
#         }
#         return json.dumps(normalized_dict, indent=4)
#     except json.JSONDecodeError as e:
#         logger.error(f"JSON decode error in normalize_keys: {e}")
#         raise
#     except Exception as e:
#         logger.error(f"Error in normalize_keys: {e}")
#         raise
#
#
# def get_blacklisted_ips(df):
#     """
#     Extracts blacklisted IP addresses for tickets with remediation enabled.
#
#     Args:
#         df (DataFrame): DataFrame containing ticket data
#
#     Returns:
#         dict: Dictionary mapping ticket IDs to lists of blacklisted IPs
#     """
#     result = {}
#     try:
#         for _, row in df.iterrows():
#             if row.get('remediation') is True and row.get('blacklisted_ips'):
#                 ip_addresses = []
#                 if isinstance(row['blacklisted_ips'], str) and row['blacklisted_ips'].startswith("["):
#                     try:
#                         ip_addresses = ast.literal_eval(row['blacklisted_ips'])
#                     except (ValueError, SyntaxError) as e:
#                         logger.error(f"Error parsing IP list for ticket {row.get('ticket', 'unknown')}: {e}")
#                 elif isinstance(row['blacklisted_ips'], list):
#                     ip_addresses = row['blacklisted_ips']
#                 elif isinstance(row['blacklisted_ips'], str):
#                     ip_addresses = [ip.strip() for ip in row['blacklisted_ips'].split(",")]
#                 if ip_addresses:
#                     result[row['ticket']] = ip_addresses
#     except Exception as e:
#         logger.error(f"Error in get_blacklisted_ips: {e}")
#     return result
#
#
# def fix_json_string(input_str):
#     """
#     Attempts to clean and repair malformed JSON strings.
#
#     Args:
#         input_str (str): JSON string to fix
#
#     Returns:
#         str or None: Fixed JSON string or None if repair failed
#     """
#     try:
#         if not input_str:
#             return None
#
#         json_str = input_str.replace('{ ', '{').replace(' {', '{').replace('} ', '}').replace(' }', '}').replace('\\','').strip().strip('"')
#
#         # First try direct parsing
#         try:
#             data = json.loads(json_str)
#             return json.dumps(data, indent=4)
#         except json.JSONDecodeError:
#             # If direct parsing fails, try repair
#             repaired = repair_json(json_str)
#             data = json.loads(repaired)
#             return json.dumps(data, indent=4)
#     except Exception as e:
#         logger.error(f"Error fixing JSON string: {e}")
#         return None
#
#
# def generate_timestamp():
#     """
#     Generates a timestamp in 'YYYYMMDD_HHMMSS' format.
#
#     Returns:
#         str: Formatted timestamp
#     """
#     return datetime.now().strftime("%Y%m%d_%H%M%S")
#
# # Define the schema for the structured output
# class ResponseFormat(BaseModel):
#     remediation : bool
#     remediation_action : str
#     remediation_reason : str
#     blacklisted_ips : list[str]
#     destination_hostnames : list[str]
#     recommendations : str
#
# def process_file(filepath, client, model):
#     """
#     Processes a single PDF file, sends it to Ollama, and updates the global DataFrame.
#
#     Args:
#         filepath (str): Path to the PDF file
#         client (ollama.Client): Ollama client instance
#         model (str): Name of the model to use
#
#     Returns:
#         bool: True if processing was successful, False otherwise
#     """
#     global df
#
#     try:
#         # Check if file exists
#         if not os.path.exists(filepath):
#             logger.error(f"File not found: {filepath}")
#             return False
#
#         # Check if file is a PDF
#         if not filepath.lower().endswith('.pdf'):
#             logger.warning(f"Skipping non-PDF file: {filepath}")
#             return False
#
#         # Extract text from PDF
#         logger.info(f"Processing file: {filepath}")
#         text = extract_pdf_text(filepath)
#
#         if not text:
#             logger.warning(f"No text extracted from {filepath}")
#             return False
#
#         parsed_text = parse_text(text)
#         prompt = "REPORTINPUT : " + parsed_text
#
#         # Generate response from Ollama
#         try:
#             response = client.generate(model=model, prompt=prompt)
#             response_string = response.response
#
#             # First try Pydantic validation
#             try:
#                 logger.info(f"Attempting Pydantic validation for {filepath}")
#                 structured_response = ResponseFormat.model_validate_json(response_string)
#                 logger.success(f"Structured Response using Pydantic: {structured_response}")
#                 # If validation succeeds, use the validated model
#                 resp_dict = structured_response.model_dump()
#                 logger.success(f"Successfully validated and parsed JSON using Pydantic")
#
#             except Exception as pydantic_error:
#                 logger.warning(f"Pydantic validation failed: {pydantic_error}")
#                 # Continue with traditional JSON parsing if Pydantic fails
#                 try:
#                     # Try to parse JSON directly, if it fails, attempt repair
#                     try:
#                         resp_dict = json.loads(response_string)
#                     except json.JSONDecodeError:
#                         fixed_json = fix_json_string(response_string)
#                         if not fixed_json:
#                             logger.error(f"Failed to repair JSON response for {filepath}")
#                             return False
#                         resp_dict = json.loads(fixed_json)
#                 except Exception as e:
#                     logger.error(f"Failed to parse Ollama response for file {filepath}: {e}")
#                     return False
#
#         # Extract metadata
#         ticket_id = parse_ticketid(parsed_text)
#         event_date = parse_datetime(parsed_text)
#         event_type = parse_event_type(parsed_text)
#         priority = parse_priority(parsed_text)
#         customer_name = parse_customer_name(parsed_text)
#
#         # Combine the Ollama response with extracted fields
#         combined_dict = {**resp_dict, **{
#             'ticket': ticket_id,
#             'date': event_date,
#             'event_type': event_type,
#             'priority': priority,
#             'customer_name': customer_name,
#             'missing_values': False  # Default value
#         }}
#
#         # Convert the combined dictionary into a DataFrame
#         new_df = pd.DataFrame([combined_dict])
#
#         # Append the new DataFrame to the global DataFrame
#         with pd.option_context('mode.chained_assignment', None):
#             df = pd.concat([df, new_df], ignore_index=True)
#
#         logger.info(f"Successfully processed file {filepath}")
#         return True
#
#     except Exception as e:
#         logger.error(f"Error processing file {filepath}: {e}")
#         return False
#
#
# class FileHandler(FileSystemEventHandler):
#     """
#     Watchdog event handler for monitoring directory changes.
#     """
#
#     def __init__(self, client, model, max_workers=4):
#         """
#         Initialize the file handler.
#
#         Args:
#             client (ollama.Client): Ollama client instance
#             model (str): Name of the model to use
#             max_workers (int): Maximum number of worker threads
#         """
#         super().__init__()
#         self.client = client
#         self.model = model
#         self.max_workers = max_workers
#         self.executor = ThreadPoolExecutor(max_workers=max_workers)
#         self.futures = []
#
#     def on_created(self, event):
#         """
#         Triggered when a new file is created in the watched directory.
#
#         Args:
#             event (FileSystemEvent): Event object containing file information
#         """
#         if stop_event.is_set():
#             return
#
#         if not event.is_directory and event.src_path.lower().endswith(".pdf"):
#             logger.info(f"New file detected: {event.src_path}")
#
#             # Submit the file processing task to the thread pool
#             future = self.executor.submit(process_file, event.src_path, self.client, self.model)
#             self.futures.append(future)
#
#     def shutdown(self):
#         """
#         Shuts down the executor and waits for all tasks to complete.
#         """
#         for future in as_completed(self.futures):
#             try:
#                 future.result()  # Get the result to catch any exceptions
#             except Exception as e:
#                 logger.error(f"Task execution error: {e}")
#
#         self.executor.shutdown(wait=True)
#         logger.info("File handler executor shut down")
#
#
# def monitor_directory(directory, client, model, max_workers=4):
#     """
#     Monitors the specified directory for new PDF files.
#
#     Args:
#         directory (str): Directory to monitor
#         client (ollama.Client): Ollama client instance
#         model (str): Name of the model to use
#         max_workers (int): Maximum number of worker threads
#
#     Returns:
#         None
#     """
#     try:
#         # Ensure directory exists
#         if not os.path.exists(directory):
#             logger.error(f"Directory does not exist: {directory}")
#             raise FileNotFoundError(f"Directory does not exist: {directory}")
#
#         # Process existing files first
#         existing_files = [os.path.join(directory, f) for f in os.listdir(directory)
#                          if os.path.isfile(os.path.join(directory, f)) and f.lower().endswith('.pdf')]
#
#         if existing_files:
#             logger.info(f"Processing {len(existing_files)} existing files...")
#             with ThreadPoolExecutor(max_workers=max_workers) as executor:
#                 futures = {executor.submit(process_file, f, client, model): f for f in existing_files}
#                 for future in as_completed(futures):
#                     file = futures[future]
#                     try:
#                         success = future.result()
#                         if success:
#                             logger.info(f"Successfully processed existing file: {file}")
#                         else:
#                             logger.warning(f"Failed to process existing file: {file}")
#                     except Exception as e:
#                         logger.error(f"Error processing existing file {file}: {e}")
#
#         # Set up watchdog for new files
#         event_handler = FileHandler(client, model, max_workers)
#         observer = Observer()
#         observer.schedule(event_handler, path=directory, recursive=False)
#         observer.start()
#         logger.info(f"Monitoring directory: {directory}")
#
#         try:
#             while not stop_event.is_set():
#                 time.sleep(1)
#         except KeyboardInterrupt:
#             logger.info("Keyboard interrupt received")
#             stop_event.set()
#         finally:
#             observer.stop()
#             event_handler.shutdown()
#             observer.join()
#             logger.info("Directory monitoring stopped")
#
#     except Exception as e:
#         logger.error(f"Error in monitor_directory: {e}")
#         raise
#
#
# def main():
#     """
#     Main function to run the PDF auditor.
#     """
#     try:
#         # Initialize Ollama client
#         client = ollama.Client()
#         model = "WebGuardian_Report_Analyzer_llama32_3B"
#
#         # Start monitoring the directory
#         monitor_directory(subdirectory, client, model)
#
#         # Save results to Excel
#         timestamp = generate_timestamp()
#         output_filename = f'Analysis_summary_{len(df)}_Files_{timestamp}_{model}.xlsx'
#
#         try:
#             df.to_excel(output_filename, index=False)
#             logger.info(f"Results saved to {output_filename}")
#         except Exception as e:
#             logger.error(f"Error saving results to Excel: {e}")
#             # Fallback to CSV if Excel fails
#             csv_filename = f'Analysis_summary_{len(df)}_Files_{timestamp}_{model}.csv'
#             df.to_csv(csv_filename, index=False)
#             logger.info(f"Results saved to {csv_filename} (fallback)")
#
#         # Calculate execution time
#         end_time = time.time()
#         execution_time = end_time - start_time
#         logger.info(f"End date and time: {datetime.now()}")
#         logger.info(f"Total time taken for execution: {execution_time:.2f} seconds")
#
#     except Exception as e:
#         logger.error(f"Error in main function: {e}")
#         raise
#
#
# if __name__ == "__main__":
#     main()
