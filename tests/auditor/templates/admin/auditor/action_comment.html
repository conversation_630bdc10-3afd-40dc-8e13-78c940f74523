{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block content %}
<div id="content-main">
    <form method="post">
        {% csrf_token %}
        <div>
            <fieldset class="module aligned">
                <h2>{{ title }}</h2>
                <div class="form-row">
                    <p>You are about to toggle the action required status for the following items:</p>
                    <ul>
                        {% for obj in queryset %}
                        <li>{{ obj }}</li>
                        {% endfor %}
                    </ul>
                    <p>Please enter an optional comment explaining this change:</p>
                    {{ form.comment.errors }}
                    {{ form.comment }}
                    <p class="help">{{ form.comment.help_text }}</p>
                </div>
            </fieldset>
        </div>
        <div class="submit-row">
            <input name="action" type="hidden" value="{{ action }}"/>
            <input class="default" name="apply" type="submit" value="Confirm"/>
            <a class="button cancel-link" href="{% url opts|admin_urlname:'changelist' %}">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}