{% extends "base.html" %}
{% block content %}
<div class="container mt-4">
  <div class="card">
    <div class="card-header">
      <h2>{{ title }}</h2>
    </div>
    <div class="card-body">
      <p>You are about to {% if audit.action_required %}disable{% else %}enable{% endif %} the action required status for:</p>
      <p class="font-weight-bold">{{ audit.ticket }} - {{ audit.customer_name }}</p>
      
      <form method="post">
        {% csrf_token %}
        <div class="form-group">
          <label for="comment">Comment (optional):</label>
          <textarea name="comment" id="comment" class="form-control" rows="3" 
                    placeholder="Please explain why you're changing this status"></textarea>
          <small class="form-text text-muted">Your username and the current time will be recorded with this change.</small>
        </div>
        
        <input type="hidden" name="next" value="{{ next }}">
        
        <div class="form-group mt-4">
          <button type="submit" class="btn btn-primary">Confirm</button>
          <a href="{{ next }}" class="btn btn-secondary">Cancel</a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}