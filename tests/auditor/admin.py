from django.contrib import admin
from django.contrib import messages
from .models import AuditResults
from django import forms

class ActionCommentForm(forms.Form):
    comment = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False,
        help_text="Optional comment about why you're changing the action status"
    )

@admin.action(description="Toggle action required status")
def toggle_action_required(modeladmin, request, queryset):
    # If this is a POST request with the comment form data
    if 'apply' in request.POST:
        comment = request.POST.get('comment', '')
        
        # Process each selected item
        updated = 0
        for item in queryset:
            item.action_required = not item.action_required
            item.save(user=request.user, comment=comment)
            updated += 1
            
        # Show success message
        modeladmin.message_user(
            request,
            f"Action required status toggled for {updated} items.",
            messages.SUCCESS,
        )
        return None
    
    # If this is the initial request, show the comment form
    context = {
        'queryset': queryset,
        'action': 'toggle_action_required',
        'form': ActionCommentForm(),
        'title': 'Enter comment for action status change',
    }
    return modeladmin.response_action(request, queryset, 'action_comment.html', context)

class AuditResultsAdmin(admin.ModelAdmin):
    list_display = ['ticket', 'customer_name', 'priority', 'missing_values', 
                   'structural_issues', 'action_required', 'updated_at']
    list_filter = ['priority', 'missing_values', 'structural_issues', 'action_required']
    search_fields = ['ticket', 'customer_name']
    readonly_fields = ['action_history']
    actions = [toggle_action_required]

admin.site.register(AuditResults, AuditResultsAdmin)