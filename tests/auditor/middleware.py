# If you have a custom authentication middleware, add this:
class CustomAuthMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Skip middleware for admin URLs
        if request.path.startswith('/admin/'):
            return self.get_response(request)
            
        # Your custom middleware logic here
        
        return self.get_response(request)