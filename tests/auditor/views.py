from django.shortcuts import render
from django.http import JsonResponse, Http404, HttpResponse
from django.core.exceptions import PermissionDenied
from .models import AuditResults
from .utils import process_file, process_directory, get_audit_progress, _update_processing_status
import os
import logging
import threading
from django.views.generic import ListView, DetailView
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django import forms
from django.db import models
from django.views.decorators.http import require_http_methods, require_POST
from django.contrib import messages
from django.shortcuts import redirect, get_object_or_404
# Import the filter from filters.py
from .filters import AuditResultsFilter
# Import FilterView from django_filters
from django_filters.views import FilterView
from django.urls import reverse
import time
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth import authenticate, login
from django.utils import timezone
import json

# Configure logging
logger = logging.getLogger(__name__)


@require_http_methods(["GET", "POST"])
def login_view(request):
    """
    Custom login view that uses our Akamai API authentication backend
    """
    # If user is already authenticated, redirect to the appropriate page
    if request.user.is_authenticated:
        return redirect('auditor:upload_file')
        
    if request.method == 'POST':
        # Authenticate using our custom backend
        user = authenticate(request)

        if user is not None:
            # Login the user
            login(request, user)

            # Redirect to the next page or default page
            next_url = request.GET.get('next', 'auditor:upload_file')
            return redirect(next_url)
        else:
            messages.error(request, 'Authentication failed. Please check your Akamai API credentials.')
            return render(request, 'login.html', {'show_instructions': True}, status=401)

    # GET request - show login form
    return render(request, 'login.html', {'admin_login_url': '/admin/login/'})

@login_required
@require_http_methods(["GET"])
def root_redirect(request):
    """
    Root URL view that redirects to the upload page
    """
    return redirect('auditor:upload_file')

@login_required
@require_http_methods(["GET"])
def audit_progress(request):
    """
    View to show audit progress and handle AJAX updates
    """
    # Get the directory path from the session
    directory_path = request.session.get('current_audit_directory')
    
    logger.info(f"Audit progress request received. Directory path: {directory_path}")
    
    if not directory_path:
        logger.warning("No directory path found in session")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'percentage': 0,
                'status': 'No active audit found',
                'complete': False
            })
        return redirect('auditor:upload_file')
    
    # Get progress data
    progress_data = get_audit_progress(directory_path)
    logger.info(f"Progress data: {progress_data}")
    
    # Add a timestamp to prevent caching issues
    progress_data['timestamp'] = time.time()
    
    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
    
    if is_ajax:
        # Return JSON response for AJAX requests
        return JsonResponse(progress_data)
    else:
        # Render the full page for direct browser requests
        return render(request, 'progress.html', progress_data)

@login_required
@require_http_methods(["GET", "POST"])
def upload_file(request):
    """View for uploading and processing files or directories"""
    if request.method == 'POST':
        try:
            directory_path = request.POST.get('directory_path')
            if not directory_path:
                messages.error(request, 'Directory path is required')
                return render(request, 'upload.html', status=400)
            
            if not os.path.exists(directory_path):
                messages.error(request, 'Directory does not exist')
                return render(request, 'upload.html', status=404)
            
            # Store the directory path in the session for progress tracking
            request.session['current_audit_directory'] = directory_path
            logger.info(f"Starting audit for directory: {directory_path}")
            
            # Start the processing in a background thread
            # The process_directory function will initialize progress tracking
            thread = threading.Thread(
                target=process_directory,
                args=(directory_path,),
                daemon=True
            )
            thread.start()
            
            # Redirect to progress page
            return redirect('auditor:audit_progress')
            
        except Exception as e:
            logger.error(f"Error starting audit: {str(e)}")
            messages.error(request, f"Error starting audit: {str(e)}")
            return render(request, 'upload.html', status=500)
    
    # GET request - show upload form
    return render(request, 'upload.html')


class AuditResultsListView(LoginRequiredMixin, FilterView):
    model = AuditResults
    filterset_class = AuditResultsFilter
    template_name = 'audit_results_list.html'  # Use the template in the central directory
    paginate_by = 10
    context_object_name = 'audit_results'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get selected fields from request
        selected_fields = self.request.GET.getlist('fields')
        
        # If no fields are selected, use default fields
        if not selected_fields:
            # Use a subset of fields as default
            selected_fields = ['ticket', 'customer_name', 'priority', 'action_required', 'remediation', 'ticket_date']
        
        logger.debug(f"Selected fields from request: {selected_fields}")
        context['selected_fields'] = selected_fields
        
        # Add total count to context
        context['total_count'] = self.filterset.qs.count()
        
        # Add query string for pagination links
        query_params = self.request.GET.copy()
        if 'page' in query_params:
            del query_params['page']
        context['query_string'] = query_params.urlencode()
        
        return context


class AuditResultDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a single audit result"""
    model = AuditResults
    template_name = "audit_result_detail.html"  # Use template from central templates directory
    context_object_name = "result"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add the correct URL name for breadcrumb navigation
        context['list_url'] = reverse('auditor:audit-results-list')
        return context
    
    def get_object(self, queryset=None):
        try:
            return super().get_object(queryset)
        except Http404:
            logger.error(f"Audit result not found: {self.kwargs.get('pk')}")
            raise
        except Exception as e:
            logger.error(f"Error retrieving audit result: {str(e)}")
            raise Http404(f"Error retrieving audit result: {str(e)}")

def get_audit_progress(directory_path):
    """
    Get the progress of an audit for a specific directory
    
    Args:
        directory_path: Path to the directory being audited
        
    Returns:
        dict: Progress data including percentage, status, and completion flag
    """
    try:
        # Import the necessary function from utils.py
        from .utils import get_audit_progress as utils_get_progress
        
        # Get the current processing status for this directory
        status = utils_get_progress(directory_path)
        
        if not status:
            # No status found, might be initializing or not started
            return {
                'percentage': 0,
                'status': 'Initializing...',
                'complete': False
            }
        
        # Return the progress information
        return {
            'percentage': status.get('percentage', 0),
            'status': status.get('status', 'Processing...'),
            'complete': status.get('complete', False)
        }
    except Exception as e:
        logger.error(f"Error getting audit progress: {str(e)}")
        return {
            'percentage': 0,
            'status': f'Error: {str(e)}',
            'complete': False
        }

@login_required
@require_http_methods(["POST"])
def stop_audit(request):
    """
    Stop the current audit process
    """
    try:
        # Get the directory path from the session
        directory_path = request.session.get('current_audit_directory')
        
        if not directory_path:
            logger.warning("No directory path found in session for stop request")
            return JsonResponse({'status': 'error', 'message': 'No active audit found'}, status=400)
        
        # Import the stop function from utils
        from .utils import stop_all_threads
        
        # Stop all processing threads
        stop_all_threads()
        
        # Update the processing status to indicate it was stopped by user
        from .utils import _update_processing_status
        
        # Get current progress to determine how many files were processed
        from .utils import get_audit_progress
        current_progress = get_audit_progress(directory_path)
        
        if current_progress:
            processed = current_progress.get('processed_files', 0)
            total = current_progress.get('total_files', 0)
            _update_processing_status(
                directory_path, 
                processed, 
                total, 
                status="Processing stopped by user", 
                complete=True
            )
        
        logger.info(f"Audit stopped for directory: {directory_path}")
        
        return JsonResponse({'status': 'success', 'message': 'Audit stopped successfully'})
    except Exception as e:
        logger.error(f"Error stopping audit: {str(e)}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


def auth_setup_instructions(request):
    """
    View to display instructions for setting up Akamai API authentication
    """
    return render(request, 'auth_setup_instructions.html')

@login_required
def test_auth_page(request):
    """
    A simple view that requires authentication to access.
    Used for testing the authentication system.
    """
    # Log authentication status
    logger.info(f"User authenticated: {request.user.is_authenticated}")
    logger.info(f"Username: {request.user.username}")
    
    context = {
        'title': 'Authentication Successful',
        'user': request.user,
        'session_key': request.session.session_key
    }
    
    return render(request, 'auth_success.html', context)

@login_required
def admin_actions(request):
    """
    View for administrative actions like clearing the database
    """
    return render(request, 'admin_actions.html', {
        'title': 'Admin Actions'
    })

@login_required
@require_http_methods(["POST"])
def clear_database(request):
    """
    View to handle the clear database action
    """
    try:
        # Get all models from all installed apps
        from django.apps import apps
        from django.db import transaction
        
        with transaction.atomic():
            # Delete all objects from all models except User model
            for model in apps.get_models():
                # Skip User model to avoid locking yourself out
                if model.__name__ not in ['User', 'Group', 'Permission']:
                    model.objects.all().delete()
                    
        messages.success(request, "Database cleared successfully!")
    except Exception as e:
        messages.error(request, f"Error clearing database: {str(e)}")
        
    return redirect('auditor:admin_actions')

@login_required
def toggle_action_required_form(request, pk):
    """Display form to toggle action_required status with comment"""
    audit = get_object_or_404(AuditResults, pk=pk)
    
    if request.method == 'POST':
        comment = request.POST.get('comment', '')
        
        # Toggle the status
        audit.action_required = not audit.action_required
        audit.save(user=request.user, comment=comment)
        
        messages.success(request, f"Action required status updated for {audit.ticket}")
        
        # Redirect back to the appropriate view
        if 'next' in request.POST:
            return redirect(request.POST.get('next'))
        return redirect('auditor:audit-results-list')
    
    # GET request - show the form
    context = {
        'audit': audit,
        'title': 'Toggle Action Required Status',
        'next': request.GET.get('next', request.META.get('HTTP_REFERER', 'auditor:audit-results-list'))
    }
    return render(request, 'action_required_form.html', context)

@login_required
@require_POST
def toggle_action_required_ajax(request, pk):
    """AJAX endpoint to toggle action_required status"""
    audit = get_object_or_404(AuditResults, pk=pk)
    comment = request.POST.get('comment', '')
    
    # Toggle the status
    audit.action_required = not audit.action_required
    audit.save(user=request.user, comment=comment)
    
    return JsonResponse({
        'success': True,
        'action_required': audit.action_required,
        'message': f"Action required status updated for {audit.ticket}"
    })

@login_required
@require_http_methods(["POST"])
def toggle_action_required(request):
    """
    Toggle the action_required status for an audit result
    
    Args:
        request: The HTTP request with JSON body containing:
                - audit_id: The ID of the audit result
                - action_required: Boolean indicating the new status
                - comment: Optional comment about the change
        
    Returns:
        JsonResponse: Success status and updated audit result
    """
    try:
        data = json.loads(request.body)
        audit_id = data.get('audit_id')
        action_required = data.get('action_required')
        comment = data.get('comment', '')
        
        # Get the audit result directly from the database
        audit_result = get_object_or_404(AuditResults, id=audit_id)
        
        # Update action_required status
        audit_result.action_required = action_required
        
        # Update action history directly in the database
        if audit_result.action_history is None:
            action_history = []
        else:
            action_history = list(audit_result.action_history)  # Make a copy to ensure it's mutable
        
        # Add new entry
        action_history.append({
            'timestamp': timezone.now().isoformat(),
            'user': request.user.username,
            'action': 'enabled' if action_required else 'disabled',
            'comment': comment
        })
        
        # Update the model with the new history
        audit_result.action_history = action_history
        
        # Save directly to the database, bypassing model logic
        AuditResults.objects.filter(id=audit_id).update(
            action_required=action_required,
            action_history=action_history,
            updated_at=timezone.now()  # Update the timestamp
        )
        
        # Log the action
        logger.info(f"Action required set to {action_required} for audit {audit_id} by {request.user.username}")
        
        return JsonResponse({
            'success': True,
            'action_required': action_required,
            'action_history': action_history
        })
    except Exception as e:
        logger.error(f"Error toggling action required: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def get_audit_history(request, audit_id):
    """
    Get the action history for an audit result
    
    Args:
        request: The HTTP request
        audit_id: The ID of the audit result
        
    Returns:
        JsonResponse: The action history as JSON
    """
    try:
        audit_result = get_object_or_404(AuditResults, id=audit_id)
        
        # Return the action history
        return JsonResponse({
            'success': True,
            'action_history': audit_result.action_history or []
        })
    except Exception as e:
        logger.error(f"Error retrieving audit history: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def debug_action_history(request, audit_id):
    """
    Debug view to check the current state of action history
    """
    try:
        audit_result = get_object_or_404(AuditResults, id=audit_id)
        return JsonResponse({
            'success': True,
            'action_required': audit_result.action_required,
            'action_history': audit_result.action_history
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
