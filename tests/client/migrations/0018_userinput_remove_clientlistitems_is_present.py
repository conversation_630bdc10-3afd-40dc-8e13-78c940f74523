# Generated by Django 5.1.6 on 2025-03-03 15:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('client', '0017_alter_clientlistitems_is_present'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserInput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('input_text', models.TextField()),
                ('matched_client_lists', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='clientlistitems',
            name='is_present',
        ),
    ]
