# Generated by Django 5.1.6 on 2025-03-22 12:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('client', '0027_delete_auditresults'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditResults',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket', models.CharField(max_length=15, unique=True)),
                ('ticket_date', models.DateTimeField(blank=True, null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('customer_name', models.CharField(max_length=100)),
                ('priority', models.Char<PERSON>ield(max_length=2)),
                ('event_type', models.Char<PERSON>ield(max_length=100)),
                ('remediation', models.BooleanField(default=False)),
                ('remediation_action', models.CharField(blank=True, max_length=1000, null=True)),
                ('remediation_reason', models.Char<PERSON><PERSON>(blank=True, max_length=1000, null=True)),
                ('blacklisted_ips', models.CharField(blank=True, max_length=1000, null=True)),
                ('wsa_link', models.CharField(blank=True, max_length=1000, null=True)),
                ('missing_values', models.BooleanField(default=False)),
                ('structural_issues', models.BooleanField(default=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
