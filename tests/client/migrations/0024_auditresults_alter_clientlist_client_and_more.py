# Generated by Django 5.1.6 on 2025-03-18 18:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('client', '0023_clientlistitems_tags_clientlistitems_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditResults',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket', models.CharField(max_length=15, unique=True)),
                ('ticket_date', models.DateTimeField(blank=True, null=True)),
                ('create_date', models.DateTimeField()),
                ('customer_name', models.CharField(max_length=100)),
                ('remediation', models.BooleanField(default=False)),
                ('remediation_action', models.CharField(blank=True, max_length=1000, null=True)),
                ('remediation_reason', models.Char<PERSON>ield(blank=True, max_length=1000, null=True)),
                ('blacklisted_ips', models.Char<PERSON>ield(blank=True, max_length=1000, null=True)),
                ('wsa_link', models.CharField(blank=True, max_length=1000, null=True)),
                ('missing_values', models.BooleanField(default=False)),
            ],
        ),
        migrations.AlterField(
            model_name='clientlist',
            name='client',
            field=models.ForeignKey(db_column='client_key', on_delete=django.db.models.deletion.CASCADE,
                                    related_name='client', to='client.client', to_field='key'),
        ),
        migrations.AlterField(
            model_name='clientlistitems',
            name='client_lists',
            field=models.ManyToManyField(related_name='client_lists', to='client.clientlist'),
        ),
    ]
