# Generated by Django 5.1.6 on 2025-02-26 18:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('client', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='client',
            name='key',
            field=models.CharField(blank=True, max_length=200, null=True, unique=True),
        ),
        migrations.CreateModel(
            name='ClientList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('listId', models.CharField(blank=True, max_length=200, null=True)),
                ('updatedBy', models.CharField(blank=True, max_length=200, null=True)),
                ('productionActivationStatus', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('createDate', models.DateTimeField()),
                ('updateDate', models.DateTimeField()),
                ('itemsCount', models.PositiveIntegerField(blank=True, null=True)),
                ('notes', models.CharField(blank=True, max_length=200, null=True)),
                ('key', models.CharField(blank=True, max_length=200, null=True)),
                ('client', models.ForeignKey(db_column='client_key', on_delete=django.db.models.deletion.CASCADE,
                                             to='client.client', to_field='key')),
            ],
        ),
    ]
