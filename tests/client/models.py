import requests
from django.db import models
from user.api_urls import profile_url, self_url, switch_keys_url
from user.models import APIUser, Session
from akamai.edgegrid import Edge<PERSON>ridAuth
from user.variables import edgerc, section


# Create your models here.
class Client(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    key = models.CharField(max_length=200, unique=True,blank=True, null=True)
    def save(self, *args, **kwargs):
        if not self.name or not self.key:
            s = requests.Session()
            s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
            sw_keys = s.get(switch_keys_url).json()
            for client in sw_keys:
                self.name = client.get('name', self.name)
                self.key = client.get('key', self.key)
        super(Client, self).save(*args,**kwargs)
    def __str__(self):
        return self.name

class ClientList(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    listId = models.CharField(max_length=200, null=True, unique=True)
    client = models.ForeignKey(Client, to_field='key', db_column='client_key', on_delete=models.CASCADE, related_name="client")
    updatedBy = models.CharField(max_length=200, blank=True, null=True)
    productionActivationStatus = models.CharField(max_length=10, blank=True, null=True)
    createDate = models.DateTimeField(blank=True, null=True)
    updateDate = models.DateTimeField(blank=True, null=True)
    itemsCount = models.PositiveIntegerField(blank=True, null=True)
    notes = models.CharField(max_length=200, blank=True, null=True)
    key = models.CharField(max_length=200, blank=True, null=True)

    # def __str__(self):
    #     return f'{self.name + "(" + self.listId + ")"}'
    def __str__(self):
        return f'{self.name}({str(self.listId)})'


class ClientListItems(models.Model):
    value = models.CharField(max_length=200, blank=True, default='')
    client_lists = models.ManyToManyField(ClientList, related_name='client_lists')
    description = models.TextField(blank=True, default='')
    stagingStatus = models.CharField(max_length=200, blank=True, null=True)
    productionStatus = models.CharField(max_length=10, blank=True, null=True)
    createDate = models.DateTimeField(blank=True, null=True)
    updateDate = models.DateTimeField(blank=True, null=True)
    expirationDate = models.DateTimeField(blank=True, null=True)
    createdBy = models.CharField(max_length=70, blank=True, null=True)
    updatedBy = models.CharField(max_length=70, blank=True, null=True)
    type = models.CharField(max_length=70, blank=True, null=True)
    tags = models.CharField(max_length=70, blank=True, null=True)

    def __str__(self):
        return self.value


class UserInput(models.Model):
    input_text = models.TextField()
    matched_client_lists = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.input_text[:50]}"



