from django.db import models
from .helpers import open_akamai_session,close_akamai_session
from .variables import edgerc_filename
from akamai.edgegrid import EdgeGridAuth, EdgeRc
import requests
import os
import pickle

home_dir = os.path.expanduser('~')
file_path = os.path.join(str(home_dir), edgerc_filename)
edgerc = EdgeRc(file_path)
section = 'default'
hostname = edgerc.get(section, 'host')
baseurl = 'https://%s' % hostname
s = requests.Session()
s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
#return s,baseurl,hostname,edgerc,section
#s,baseurl,hostname, edgerc, section = open_akamai_session()


# Create your models here.
class APIUser(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    #url = models.CharField(max_length=200, blank=True, null=True)
    username = models.CharField(max_length=8, blank=True, null=True)
    email = models.EmailField(max_length=100, blank=True, null=True, unique=True)
    organization = models.CharField(max_length=100, blank=True, null=True)
    accountSwitch = models.BooleanField(default=False)

    def update_profile(self, s, profile_url, self_url):
        home_dir = os.path.expanduser('~')
        file_path = os.path.join(str(home_dir), edgerc_filename)
        edgerc = EdgeRc(file_path)
        section = 'default'
        hostname = edgerc.get(section, 'host')
        baseurl = 'https://%s' % hostname
        s = requests.Session()
        s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
        profile = s.get(profile_url).json()
        self_details = s.get(self_url).json()
        self.name = profile['firstName'] + " " + profile['lastName']
        #self.url = s.auth.baseurl
        self.username = profile['uiUserName']
        self.email = profile['email']
        self.organization = self.email.split('@')[1].split('.')[0]
        self.accountSwitch = self_details["canAutoCreateCredential"]
        self.save()
    def __str__(self):
        return self.name

class Session(models.Model):
    user = models.ForeignKey(APIUser, on_delete=models.CASCADE)
    session_object = models.TextField()
    auth_object = models.TextField()

    def set_session_and_auth(self, edgerc, section):
        s = requests.Session()
        s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
        serialized_session = pickle.dumps(s)
        serialized_auth = pickle.dumps(s.auth)
        self.session_object = serialized_session
        self.auth_object = serialized_auth
        self.save()

    def get_session_and_auth(self):
        session = pickle.loads(self.session_object)
        auth = pickle.loads(self.auth_object)
        session.auth = auth
        return session