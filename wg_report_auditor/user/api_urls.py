from .variables import hostname

profile_url =  f"https://{hostname}/identity-management/v3/user-profile"
self_url =  f"https://{hostname}/identity-management/v3/api-clients/self"
switch_keys_url = f"https://{hostname}/identity-management/v3/api-clients/self/account-switch-keys"
accessible_client_lists_url = f'https://{hostname}/client-list/v1/lists'

#https://techdocs.akamai.com/client-lists/reference/get-list
get_list_items = f'https://{hostname}/client-list/v1/lists/<listId>/items'
# Same endpoint can also update the items. However, the HTTP method is POST in this case, with some Parameters that need to be added.
#https://techdocs.akamai.com/client-lists/reference/post-update-items
update_list_items = f'https://{hostname}/client-list/v1/lists/<listId>/items'

# Contracts and Groups
find_contracts_groups = f"https://{hostname}/client-list/v1/contracts-groups"

# Traffic by hostname
traffic_report_url = f"https://{hostname}/reporting-api/v2/reports/delivery/traffic/current/data"