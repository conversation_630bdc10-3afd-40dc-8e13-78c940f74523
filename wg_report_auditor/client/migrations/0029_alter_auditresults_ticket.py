# Generated by Django 5.1.6 on 2025-03-22 12:23

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('client', '0028_auditresults'),
    ]

    operations = [
        migrations.AlterField(
            model_name='auditresults',
            name='ticket',
            field=models.CharField(max_length=15, unique=True, validators=[
                django.core.validators.RegexValidator(message='Ticket must be alphanumeric', regex='^[A-Za-z0-9]+$')]),
        ),
    ]
