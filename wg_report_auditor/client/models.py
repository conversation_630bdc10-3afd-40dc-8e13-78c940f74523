import requests
from django.db import models
from user.api_urls import profile_url, self_url, switch_keys_url, get_cp_codes
from user.models import APIUser, Session
from akamai.edgegrid import EdgeGridAuth
from user.variables import edgerc, section


# Create your models here.
class Client(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    key = models.CharField(max_length=200, unique=True,blank=True, null=True)
    cpcodes = models.JSONField(blank=True, null=True)
    def save(self, *args, **kwargs):
        # Extract bypass_api from kwargs if present
        bypass_api = kwargs.pop('bypass_api', False)

        # Only make API call if not bypassing and missing name, key, or cpcodes
        if not bypass_api and (not self.name or not self.key or not self.cpcodes):
            s = requests.Session()
            s.auth = EdgeGridAuth.from_edgerc(edgerc, section)

            # Get switch keys if name or key is missing
            if not self.name or not self.key:
                sw_keys = s.get(switch_keys_url).json()
                for client in sw_keys:
                    self.name = client.get('name', self.name)
                    self.key = client.get('key', self.key)

            # Get CP codes if missing and we have a key
            if not self.cpcodes and self.key:
                try:
                    headers = {"accept": "application/json"}
                    params = {
                        "actions": True,
                        "authGrants": True,
                        "notifications": True,
                        "accountSwitchKey": self.key
                    }

                    cp_codes_response = s.get(get_cp_codes, params=params, headers=headers)
                    if cp_codes_response.status_code == 200:
                        self.cpcodes = cp_codes_response.json()
                        print(f"DEBUG: Fetched CP codes for {self.name}: {len(self.cpcodes.get('cpcodes', []))} codes")
                    else:
                        print(f"DEBUG: Failed to fetch CP codes for {self.name}: {cp_codes_response.status_code}")

                except Exception as e:
                    print(f"DEBUG: Error fetching CP codes for {self.name}: {str(e)}")

        super(Client, self).save(*args, **kwargs)
    def __str__(self):
        return self.name


#
# class Client(models.Model):
#     name = models.CharField(max_length=200, blank=True, null=True)
#     key = models.CharField(max_length=200, unique=True, blank=True, null=True)
#     cpcodes = models.JSONField(blank=True, null=True)
#
#     def save(self, *args, **kwargs):
#         bypass_api = kwargs.pop('bypass_api', False)
#
#         if not bypass_api:
#             session = requests.Session()
#             try:
#                 session.auth = EdgeGridAuth.from_edgerc(edgerc, section)
#             except Exception as e:
#                 print(f"[EdgeGrid Setup Error] {e}")
#                 return super().save(*args, **kwargs)  # Allow save without API setup
#
#             # Step 1: Fetch name/key if missing
#             if not self.name or not self.key:
#                 try:
#                     resp = session.get(switch_keys_url)
#                     resp.raise_for_status()
#                     sw_keys = resp.json()
#
#                     for client in sw_keys:
#                         if not self.name and client.get("name"):
#                             self.name = client["name"]
#                         if not self.key and client.get("key"):
#                             self.key = client["key"]
#                         if self.name and self.key:
#                             break
#                 except Exception as e:
#                     print(f"[Switch Key Fetch Error] {e}")
#
#             # Step 2: Fetch CP codes for the found key
#             if self.key:
#                 try:
#                     headers = {"accept": "application/json"}
#                     params = {
#                         "actions": True,
#                         "authGrants": True,
#                         "notifications": True,
#                         "accountSwitchKey": self.key
#                     }
#
#                     resp = session.get(get_cp_codes_url, params=params, headers=headers)
#                     resp.raise_for_status()
#                     cp_codes_response = resp.json()
#
#                     self.cpcodes = cp_codes_response
#
#                 except Exception as e:
#                     print(f"[CP Codes Fetch Error] {e}")
#
#         return super().save(*args, **kwargs)
#
#     def __str__(self):
#         return self.name or "Unnamed Client"
#

class ClientList(models.Model):
    name = models.CharField(max_length=200, blank=True, null=True)
    listId = models.CharField(max_length=200, null=True, unique=True)
    client = models.ForeignKey(Client, to_field='key', db_column='client_key', on_delete=models.CASCADE, related_name="client")
    updatedBy = models.CharField(max_length=200, blank=True, null=True)
    productionActivationStatus = models.CharField(max_length=10, blank=True, null=True)
    createDate = models.DateTimeField(blank=True, null=True)
    updateDate = models.DateTimeField(blank=True, null=True)
    itemsCount = models.PositiveIntegerField(blank=True, null=True)
    notes = models.CharField(max_length=200, blank=True, null=True)
    key = models.CharField(max_length=200, blank=True, null=True)

    # def __str__(self):
    #     return f'{self.name + "(" + self.listId + ")"}'
    def __str__(self):
        return f'{self.name}({str(self.listId)})'


class ClientListItems(models.Model):
    value = models.CharField(max_length=200, blank=True, default='')
    client_lists = models.ManyToManyField(ClientList, related_name='client_lists')
    description = models.TextField(blank=True, default='')
    stagingStatus = models.CharField(max_length=200, blank=True, null=True)
    productionStatus = models.CharField(max_length=10, blank=True, null=True)
    createDate = models.DateTimeField(blank=True, null=True)
    updateDate = models.DateTimeField(blank=True, null=True)
    expirationDate = models.DateTimeField(blank=True, null=True)
    createdBy = models.CharField(max_length=70, blank=True, null=True)
    updatedBy = models.CharField(max_length=70, blank=True, null=True)
    type = models.CharField(max_length=70, blank=True, null=True)
    tags = models.CharField(max_length=70, blank=True, null=True)

    def __str__(self):
        return self.value


class UserInput(models.Model):
    input_text = models.TextField()
    matched_client_lists = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.input_text[:50]}"



