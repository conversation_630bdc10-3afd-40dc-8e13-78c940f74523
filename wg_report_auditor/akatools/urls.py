from django.urls import path
from . import views
from .traffic_report_view import traffic_report_view, traffic_report_api, download_traffic_report,load_switch_keys, load_cpcodes

app_name = 'akatools'

urlpatterns = [
    path('clients/', views.home, name='clients'),
    path('client-lists/', views.load_client_lists, name='client_lists'),
    path('display-selection/', views.display_selection, name='display_selection'),
    path('refresh/', views.refresh_data, name='refresh_data'),
    path('ip-address/', views.ip_address_view, name='ip_address'),
    path('items-form-page/', views.items_form_page, name='items_form_page'),
    path('client-list-items/', views.populate_clientlist_items, name='populate_clientlist_items'),
    path('check-results/', views.check_results_for_input, name='check_results_for_input'),



    # Traffic Report URLs
    path('traffic-report/', traffic_report_view, name='traffic_report'),
    path('traffic-report/api/', traffic_report_api, name='traffic_report_api'),
    path('traffic-report/download/', download_traffic_report, name='download_traffic_report'),
    path('load-switch-keys/', load_switch_keys, name='load_switch_keys'),
    path('load-cpcodes/', load_cpcodes, name='load_cpcodes'),
]
