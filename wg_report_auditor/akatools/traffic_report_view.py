"""
Django view integration for Akamai Traffic Reports

This module provides Django views for generating and displaying traffic reports
with beautiful charts integrated into the web interface.
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.conf import settings
import json
import logging
from datetime import datetime
from .trafficreport import generate_traffic_report, AkamaiTrafficReporter
from user.variables import DURATION_OPTIONS, DURATION_LABELS

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["GET", "POST"])
def traffic_report_view(request):
    """
    Django view for generating and displaying traffic reports
    """
    # Create a list of tuples (value, label) for the dropdown
    duration_choices = [(option, DURATION_LABELS[option]) for option in DURATION_OPTIONS]
    
    context = {
        'charts': {},
        'stats': {},
        'form_data': {
            'duration': 'LAST_1_WEEK',
            'cpcode': '1657476',
            'limit': 20,
            'switch_key': ''
        },
        'duration_choices': duration_choices
    }
    
    if request.method == 'POST':
        try:
            # Get form data
            duration = request.POST.get('duration', 'LAST_1_WEEK')
            cpcode = request.POST.get('cpcode', '1657476')
            limit = int(request.POST.get('limit', 20))
            switch_key = request.POST.get('switch_key', '')
            
            # Update form data for template
            context['form_data'].update({
                'duration': duration,
                'cpcode': cpcode,
                'limit': limit,
                'switch_key': switch_key
            })
            
            # Generate report
            logger.info(f"Generating traffic report for duration: {duration}, cpcode: {cpcode}")
            charts, data = generate_traffic_report(
                switch_key=switch_key,
                duration=duration,
                cpcode=cpcode,
                limit=limit,
                save_to_file=False  # Don't save to file in web interface
            )
            
            if charts and data is not None:
                # Generate summary stats
                reporter = AkamaiTrafficReporter()
                stats = reporter.generate_summary_stats(data)
                
                context.update({
                    'charts': charts,
                    'stats': stats,
                    'data_count': len(data),
                    'success': True
                })
                
                messages.success(request, f'Traffic report generated successfully! Found {len(data)} hostname records.')
                logger.info(f"Successfully generated traffic report with {len(charts)} charts")
                
            else:
                messages.error(request, 'Failed to generate traffic report. Please check your credentials and try again.')
                logger.error("Failed to generate traffic report")
                
        except ValueError as e:
            messages.error(request, f'Invalid input: {str(e)}')
            logger.error(f"Invalid input in traffic report: {str(e)}")
        except Exception as e:
            messages.error(request, f'Error generating report: {str(e)}')
            logger.error(f"Error in traffic report view: {str(e)}")
    
    return render(request, 'akatools/traffic_report.html', context)

@login_required
def download_traffic_report(request):
    """
    Download traffic report as HTML file
    """
    try:
        # Get parameters from request
        duration = request.GET.get('duration', 'LAST_1_WEEK')
        cpcode = request.GET.get('cpcode', '1657476')
        limit = int(request.GET.get('limit', 20))
        switch_key = request.GET.get('switch_key', '')
        
        # Generate report
        charts, data = generate_traffic_report(
            switch_key=switch_key,
            duration=duration,
            cpcode=cpcode,
            limit=limit,
            save_to_file=False
        )
        
        if not charts or data is None:
            messages.error(request, 'Failed to generate report for download.')
            return redirect('akatools:traffic_report')
        
        # Generate HTML content
        reporter = AkamaiTrafficReporter()
        stats = reporter.generate_summary_stats(data)
        
        context = {
            'charts': charts,
            'stats': stats,
            'data_count': len(data),
            'form_data': {
                'duration': duration,
                'cpcode': cpcode,
                'limit': limit,
                'switch_key': switch_key
            },
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'is_download': True
        }
        
        html_content = render(request, 'akatools/traffic_report_download.html', context).content.decode('utf-8')
        
        # Create response with HTML content
        response = HttpResponse(html_content, content_type='text/html')
        response['Content-Disposition'] = f'attachment; filename="traffic_report_{cpcode}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html"'
        
        return response
        
    except Exception as e:
        messages.error(request, f'Error generating report for download: {str(e)}')
        logger.error(f"Error in download traffic report: {str(e)}")
        return redirect('akatools:traffic_report')

@login_required
def traffic_report_api(request):
    """
    API endpoint for traffic report data
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Only GET requests are supported'}, status=405)
    
    try:
        # Get parameters from request
        duration = request.GET.get('duration', 'LAST_1_WEEK')
        cpcode = request.GET.get('cpcode', '1657476')
        limit = int(request.GET.get('limit', 20))
        switch_key = request.GET.get('switch_key', '')
        
        # Generate report
        charts, data = generate_traffic_report(
            switch_key=switch_key,
            duration=duration,
            cpcode=cpcode,
            limit=limit,
            save_to_file=False
        )
        
        if not charts or data is None:
            return JsonResponse({'error': 'Failed to generate report'}, status=500)
        
        # Return data as JSON
        return JsonResponse({
            'charts': charts,
            'data': data,
            'count': len(data)
        })
        
    except Exception as e:
        logger.error(f"Error in traffic report API: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)

