"""
Django view integration for Akamai Traffic Reports

This module provides Django views for generating and displaying traffic reports
with beautiful charts integrated into the web interface.
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.conf import settings
import json
import logging
from datetime import datetime
from .trafficreport import generate_traffic_report, AkamaiTrafficReporter
from user.variables import DURATION_OPTIONS, DURATION_LABELS
from client.models import Client

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["GET", "POST"])
def traffic_report_view(request):
    """
    Django view for generating and displaying traffic reports
    """
    logger.info("Traffic report view accessed")

    # Create a list of tuples (value, label) for the dropdown
    duration_choices = [(option, DURATION_LABELS[option]) for option in DURATION_OPTIONS]

    # Load all clients for the dropdown
    clients = Client.objects.all().order_by('name')
    logger.info(f"Loaded {clients.count()} clients for dropdown")

    context = {
        'charts': {},
        'stats': {},
        'form_data': {
            'duration': 'LAST_1_WEEK',
            'client': '',
            'switch_key': '',
            'cpcode': '',
            'limit': 20
        },
        'duration_choices': duration_choices,
        'clients': clients
    }
    
    if request.method == 'POST':
        try:
            # Get form data
            duration = request.POST.get('duration', 'LAST_1_WEEK')
            client = request.POST.get('client', '')
            switch_key = request.POST.get('switch_key', '')
            cpcode = request.POST.get('cpcode', '')
            limit = int(request.POST.get('limit', 20))

            logger.info(f"Form submitted - Client: {client}, Switch Key: {switch_key}, CP Code: {cpcode}")

            # Update form data for template
            context['form_data'].update({
                'duration': duration,
                'client': client,
                'switch_key': switch_key,
                'cpcode': cpcode,
                'limit': limit
            })
            
            # Generate report
            logger.info(f"Generating traffic report for duration: {duration}, cpcode: {cpcode}")
            charts, data = generate_traffic_report(
                switch_key=switch_key,
                duration=duration,
                cpcode=cpcode,
                limit=limit,
                save_to_file=False  # Don't save to file in web interface
            )
            
            if charts and data is not None:
                # Generate summary stats
                reporter = AkamaiTrafficReporter()
                stats = reporter.generate_summary_stats(data)
                
                context.update({
                    'charts': charts,
                    'stats': stats,
                    'data_count': len(data),
                    'success': True
                })
                
                messages.success(request, f'Traffic report generated successfully! Found {len(data)} hostname records.')
                logger.info(f"Successfully generated traffic report with {len(charts)} charts")
                
            else:
                messages.error(request, 'Failed to generate traffic report. Please check your credentials and try again.')
                logger.error("Failed to generate traffic report")
                
        except ValueError as e:
            messages.error(request, f'Invalid input: {str(e)}')
            logger.error(f"Invalid input in traffic report: {str(e)}")
        except Exception as e:
            messages.error(request, f'Error generating report: {str(e)}')
            logger.error(f"Error in traffic report view: {str(e)}")
    
    return render(request, 'akatools/traffic_report.html', context)

@login_required
def download_traffic_report(request):
    """
    Download traffic report as HTML file
    """
    try:
        # Get parameters from request
        duration = request.GET.get('duration', 'LAST_1_WEEK')
        cpcode = request.GET.get('cpcode', '1657476')
        limit = int(request.GET.get('limit', 20))
        switch_key = request.GET.get('switch_key', '')
        
        logger.info(f"Downloading traffic report with params: duration={duration}, cpcode={cpcode}, limit={limit}, switch_key={switch_key}")
        
        # Generate report
        charts, data = generate_traffic_report(
            switch_key=switch_key,
            duration=duration,
            cpcode=cpcode,
            limit=limit,
            save_to_file=False
        )
        
        if not charts or data is None:
            logger.error("Failed to generate report for download")
            messages.error(request, 'Failed to generate report for download.')
            return redirect('akatools:traffic_report')
        
        # Generate HTML content
        reporter = AkamaiTrafficReporter()
        stats = reporter.generate_summary_stats(data)
        
        context = {
            'charts': charts,
            'stats': stats,
            'data_count': len(data),
            'form_data': {
                'duration': duration,
                'cpcode': cpcode,
                'limit': limit,
                'switch_key': switch_key
            },
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'is_download': True
        }
        
        logger.info("Rendering download template")
        html_content = render(request, 'akatools/traffic_report_download.html', context).content.decode('utf-8')
        
        # Create response with HTML content
        filename = f"traffic_report_{cpcode}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        logger.info(f"Creating download response with filename: {filename}")
        
        response = HttpResponse(html_content, content_type='text/html')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        logger.error(f"Error in download traffic report: {str(e)}")
        messages.error(request, f'Error generating report for download: {str(e)}')
        return redirect('akatools:traffic_report')

@login_required
def traffic_report_api(request):
    """
    API endpoint for traffic report data
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Only GET requests are supported'}, status=405)
    
    try:
        # Get parameters from request
        duration = request.GET.get('duration', 'LAST_1_WEEK')
        cpcode = request.GET.get('cpcode', '1657476')
        limit = int(request.GET.get('limit', 20))
        switch_key = request.GET.get('switch_key', '')
        
        # Generate report
        charts, data = generate_traffic_report(
            switch_key=switch_key,
            duration=duration,
            cpcode=cpcode,
            limit=limit,
            save_to_file=False
        )
        
        if not charts or data is None:
            return JsonResponse({'error': 'Failed to generate report'}, status=500)
        
        # Return data as JSON
        return JsonResponse({
            'charts': charts,
            'data': data,
            'count': len(data)
        })
        
    except Exception as e:
        logger.error(f"Error in traffic report API: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def load_switch_keys(request):
    """
    HTMX endpoint to load switch keys for a selected client
    """
    client_key = request.GET.get('client')
    logger.info(f"Loading switch keys for client: {client_key}")

    if not client_key:
        logger.warning("No client key provided")
        return render(request, 'akatools/switch_key_dropdown.html', {
            'switch_keys': [],
            'selected_switch_key': ''
        })

    try:
        # Get the selected client
        client = Client.objects.get(key=client_key)
        logger.info(f"Found client: {client.name}")

        # For now, we'll use the client's key as the switch key
        # In a real scenario, you might have multiple switch keys per client
        switch_keys = [{'key': client.key, 'name': client.name}]

        logger.info(f"Returning {len(switch_keys)} switch keys")

        return render(request, 'akatools/switch_key_dropdown.html', {
            'switch_keys': switch_keys,
            'selected_switch_key': ''
        })

    except Client.DoesNotExist:
        logger.error(f"Client not found: {client_key}")
        return render(request, 'akatools/switch_key_dropdown.html', {
            'switch_keys': [],
            'selected_switch_key': ''
        })
    except Exception as e:
        logger.error(f"Error loading switch keys: {str(e)}")
        return render(request, 'akatools/switch_key_dropdown.html', {
            'switch_keys': [],
            'selected_switch_key': ''
        })


@login_required
def load_cpcodes(request):
    """
    HTMX endpoint to load CP codes for a selected client and switch key
    """
    client_key = request.GET.get('client')
    switch_key = request.GET.get('switch_key')

    logger.info(f"Loading CP codes for client: {client_key}, switch_key: {switch_key}")

    if not client_key or not switch_key:
        logger.warning(f"Missing parameters - client: {client_key}, switch_key: {switch_key}")
        return render(request, 'akatools/cpcode_dropdown.html', {
            'cpcodes': [],
            'selected_cpcode': ''
        })

    try:
        # Get the selected client
        client = Client.objects.get(key=client_key)
        logger.info(f"Found client: {client.name}")

        # Extract CP codes from the client's cpcodes JSONField
        cpcodes = []
        if client.cpcodes:
            logger.info(f"Client has cpcodes data: {type(client.cpcodes)}")

            # Handle different possible structures of the cpcodes data
            if isinstance(client.cpcodes, list):
                for cpcode_data in client.cpcodes:
                    if isinstance(cpcode_data, dict):
                        cpcode_id = cpcode_data.get('cpcodeId') or cpcode_data.get('id')
                        cpcode_name = cpcode_data.get('cpcodeName') or cpcode_data.get('name', '')
                        if cpcode_id:
                            display_name = f"{cpcode_id} ({cpcode_name})" if cpcode_name else str(cpcode_id)
                            cpcodes.append({
                                'id': cpcode_id,
                                'name': cpcode_name,
                                'display': display_name
                            })
            elif isinstance(client.cpcodes, dict):
                # Handle case where cpcodes is a dict with nested structure
                cpcode_list = client.cpcodes.get('cpcodes', []) or client.cpcodes.get('data', [])
                for cpcode_data in cpcode_list:
                    if isinstance(cpcode_data, dict):
                        cpcode_id = cpcode_data.get('cpcodeId') or cpcode_data.get('id')
                        cpcode_name = cpcode_data.get('cpcodeName') or cpcode_data.get('name', '')
                        if cpcode_id:
                            display_name = f"{cpcode_id} ({cpcode_name})" if cpcode_name else str(cpcode_id)
                            cpcodes.append({
                                'id': cpcode_id,
                                'name': cpcode_name,
                                'display': display_name
                            })

        logger.info(f"Extracted {len(cpcodes)} CP codes from client data")
        for cpcode in cpcodes[:5]:  # Log first 5 for debugging
            logger.info(f"CP Code: {cpcode}")

        return render(request, 'akatools/cpcode_dropdown.html', {
            'cpcodes': cpcodes,
            'selected_cpcode': ''
        })

    except Client.DoesNotExist:
        logger.error(f"Client not found: {client_key}")
        return render(request, 'akatools/cpcode_dropdown.html', {
            'cpcodes': [],
            'selected_cpcode': ''
        })
    except Exception as e:
        logger.error(f"Error loading CP codes: {str(e)}")
        return render(request, 'akatools/cpcode_dropdown.html', {
            'cpcodes': [],
            'selected_cpcode': ''
        })

