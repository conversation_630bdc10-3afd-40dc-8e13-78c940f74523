"""
Django view integration for Akamai Traffic Reports

This module provides Django views for generating and displaying traffic reports
with beautiful charts integrated into the web interface.
"""

from django.shortcuts import render, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.conf import settings
import json
import logging
from datetime import datetime
from .trafficreport import generate_traffic_report, AkamaiTrafficReporter
from user.variables import DURATION_OPTIONS, DURATION_LABELS
from client.models import Client  # Add this import

from loguru import logger
#logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["GET", "POST"])
def traffic_report_view(request):
    """
    Django view for generating and displaying traffic reports
    """
    # Create a list of tuples (value, label) for the dropdown
    duration_choices = [(option, DURATION_LABELS[option]) for option in DURATION_OPTIONS]
    
    # Get all clients for the dropdown
    clients = Client.objects.all().order_by('name')
    
    # Default context
    context = {
        'charts': {},
        'stats': {},
        'form_data': {
            'duration': 'LAST_1_WEEK',
            'cpcode': '',
            'limit': 20,
            'client': '',
            'switch_key': ''
        },
        'duration_choices': duration_choices,
        'clients': clients
    }
    
    if request.method == 'POST':
        try:
            # Get form data
            duration = request.POST.get('duration', 'LAST_1_WEEK')
            cpcode = request.POST.get('cpcode', '')
            limit = int(request.POST.get('limit', 20))
            client_key = request.POST.get('client', '')
            switch_key = request.POST.get('switch_key', '')
            
            # Update form data for template
            context['form_data'].update({
                'duration': duration,
                'cpcode': cpcode,
                'limit': limit,
                'client': client_key,
                'switch_key': switch_key
            })
            
            # Generate report only if we have all required fields
            if cpcode and switch_key:
                logger.info(f"Generating traffic report for duration: {duration}, cpcode: {cpcode}")
                charts, data = generate_traffic_report(
                    switch_key=switch_key,
                    duration=duration,
                    cpcode=cpcode,
                    limit=limit,
                    save_to_file=False  # Don't save to file in web interface
                )
                
                if charts and data is not None:
                    # Generate summary stats
                    reporter = AkamaiTrafficReporter()
                    stats = reporter.generate_summary_stats(data)
                    
                    context.update({
                        'charts': charts,
                        'stats': stats,
                        'data_count': len(data),
                        'success': True
                    })
                    
                    messages.success(request, f'Traffic report generated successfully! Found {len(data)} hostname records.')
                    logger.info(f"Successfully generated traffic report with {len(charts)} charts")
                    
                else:
                    messages.error(request, 'Failed to generate traffic report. Please check your credentials and try again.')
                    logger.error("Failed to generate traffic report")
            else:
                messages.error(request, 'Please select a client, switch key, and CP code to generate a report.')
                
        except ValueError as e:
            messages.error(request, f'Invalid input: {str(e)}')
            logger.error(f"Invalid input in traffic report: {str(e)}")
        except Exception as e:
            messages.error(request, f'Error generating report: {str(e)}')
            logger.error(f"Error in traffic report view: {str(e)}")
    
    return render(request, 'akatools/traffic_report.html', context)

@login_required
def traffic_report_api(request):
    """
    API endpoint for generating traffic reports (returns JSON)
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)
    
    try:
        # Parse JSON data
        data = json.loads(request.body)
        
        duration = data.get('duration', 'LAST_1_WEEK')
        cpcode = data.get('cpcode', '1657476')
        limit = int(data.get('limit', 20))
        switch_key = data.get('switch_key', 'F-AC-4891513:1-5G3LB')
        
        # Generate report
        charts, df = generate_traffic_report(
            switch_key=switch_key,
            duration=duration,
            cpcode=cpcode,
            limit=limit,
            save_to_file=False
        )
        
        if charts and df is not None:
            # Generate summary stats
            reporter = AkamaiTrafficReporter()
            stats = reporter.generate_summary_stats(df)
            
            # Convert DataFrame to dict for JSON serialization
            data_records = df.to_dict('records') if not df.empty else []
            
            return JsonResponse({
                'success': True,
                'stats': stats,
                'data_count': len(df),
                'data': data_records[:10],  # Return only first 10 records
                'charts_available': list(charts.keys())
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to generate traffic report'
            }, status=500)
            
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error in traffic report API: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@login_required
def download_traffic_report(request):
    """
    Download traffic report as HTML file
    """
    try:
        # Get parameters from request
        duration = request.GET.get('duration', 'LAST_1_WEEK')
        cpcode = request.GET.get('cpcode', '')
        limit = int(request.GET.get('limit', 20))
        client = request.GET.get('client', '')
        switch_key = request.GET.get('switch_key', '')
        
        if not cpcode or not switch_key:
            messages.error(request, 'Please select a client, switch key, and CP code to download a report.')
            return redirect('akatools:traffic_report')
        
        # Generate report
        charts, data = generate_traffic_report(
            switch_key=switch_key,
            duration=duration,
            cpcode=cpcode,
            limit=limit,
            save_to_file=False
        )
        
        if not charts or data is None:
            messages.error(request, 'Failed to generate traffic report for download')
            return redirect('akatools:traffic_report')
        
        # Create HTML content
        reporter = AkamaiTrafficReporter()
        
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Akamai Traffic Report</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                .chart-container { margin-bottom: 30px; }
                .stats-card { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <h1 class="text-center mb-4">Akamai Traffic Report</h1>
                        <p class="text-center text-muted">Generated on {timestamp}</p>
                        <p class="text-center text-muted">Duration: {duration} | CP Code: {cpcode}</p>
                    </div>
                </div>
                
                <div class="row">
                    {chart_content}
                </div>
            </div>
            
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        </body>
        </html>
        """
        
        chart_content = ""
        for chart_name, chart_html in charts.items():
            chart_content += f"""
            <div class="col-12 chart-container">
                <div class="card">
                    <div class="card-body">
                        {chart_html}
                    </div>
                </div>
            </div>
            """
        
        final_html = html_template.format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            duration=duration,
            cpcode=cpcode,
            chart_content=chart_content
        )
        
        # Create HTTP response with HTML file
        response = HttpResponse(final_html, content_type='text/html')
        response['Content-Disposition'] = f'attachment; filename="traffic_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html"'
        
        return response
        
    except Exception as e:
        logger.error(f"Error downloading traffic report: {str(e)}")
        messages.error(request, f'Error downloading report: {str(e)}')
        return redirect('akatools:traffic_report')

@login_required
def load_switch_keys(request):
    """
    HTMX view to load switch keys based on selected client
    """
    client_key = request.GET.get('client', '')
    selected_switch_key = request.GET.get('switch_key', '')
    
    if client_key:
        try:
            client = Client.objects.get(key=client_key)
            return render(request, 'akatools/switch_key_dropdown.html', {
                'client': client,
                'selected_switch_key': selected_switch_key
            })
        except Client.DoesNotExist:
            return HttpResponse('<select name="switch_key" id="switch_key" class="form-control"><option value="">Client not found</option></select>')
    else:
        return HttpResponse('<select name="switch_key" id="switch_key" class="form-control" disabled><option value="">Select a client first</option></select>')

@login_required
def load_cpcodes(request):
    """
    HTMX view to load CP codes based on selected client
    """
    client_key = request.GET.get('client', '')
    selected_cpcode = request.GET.get('cpcode', '')
    
    logger.info(f"load_cpcodes called with client_key: {client_key}, selected_cpcode: {selected_cpcode}")
    
    if client_key:
        try:
            client = Client.objects.get(key=client_key)
            logger.info(f"Found client: {client.name} with key: {client.key}")
            
            # Extract CP codes from the JSONField
            cpcodes = []
            logger.info(f"Client cpcodes data type: {type(client.cpcodes)}")
            logger.info(f"Client cpcodes content: {client.cpcodes}")
            
            if client.cpcodes:
                # Check the structure of the cpcodes JSON
                if isinstance(client.cpcodes, dict) and 'objects' in client.cpcodes:
                    logger.info(f"Found 'objects' key in cpcodes. Count: {len(client.cpcodes['objects'])}")
                    for cpcode in client.cpcodes['objects']:
                        cpcode_id = cpcode.get('cpcodeId', '')
                        cpcode_name = cpcode.get('cpcodeName', '')
                        logger.info(f"Processing cpcode: ID={cpcode_id}, Name={cpcode_name}")
                        if cpcode_id and cpcode_name:
                            cpcodes.append({
                                'id': cpcode_id,
                                'name': cpcode_name,
                                'display': f"{cpcode_id} ({cpcode_name})"
                            })
                elif isinstance(client.cpcodes, list):
                    logger.info(f"cpcodes is a list. Count: {len(client.cpcodes)}")
                    for cpcode in client.cpcodes:
                        if isinstance(cpcode, dict):
                            cpcode_id = cpcode.get('cpcodeId', '')
                            cpcode_name = cpcode.get('cpcodeName', '')
                            logger.info(f"Processing cpcode: ID={cpcode_id}, Name={cpcode_name}")
                            if cpcode_id and cpcode_name:
                                cpcodes.append({
                                    'id': cpcode_id,
                                    'name': cpcode_name,
                                    'display': f"{cpcode_id} ({cpcode_name})"
                                })
                else:
                    logger.warning(f"Unexpected cpcodes structure: {type(client.cpcodes)}")
            
            logger.info(f"Final cpcodes list: {cpcodes}")
            
            return render(request, 'akatools/cpcode_dropdown.html', {
                'cpcodes': cpcodes,
                'selected_cpcode': selected_cpcode
            })
        except Client.DoesNotExist:
            logger.error(f"Client with key {client_key} not found")
            return HttpResponse('<select name="cpcode" id="cpcode" class="form-control"><option value="">Client not found</option></select>')
        except Exception as e:
            logger.error(f"Error in load_cpcodes: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return HttpResponse(f'<select name="cpcode" id="cpcode" class="form-control"><option value="">Error: {str(e)}</option></select>')
    else:
        logger.warning("No client_key provided")
        return HttpResponse('<select name="cpcode" id="cpcode" class="form-control" disabled><option value="">Select a client first</option></select>')