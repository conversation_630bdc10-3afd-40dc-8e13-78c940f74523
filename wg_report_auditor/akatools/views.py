from django.http import HttpResponse, HttpResponseBadRequest
from django.shortcuts import render,redirect, get_object_or_404
from django.db import IntegrityError, transaction
from client.models import ClientList,Client, ClientListItems
from .forms import ClientForm, ItemsForm
from user.models import APIUser
from user.models import open_akamai_session,close_akamai_session
from user.api_urls import switch_keys_url, accessible_client_lists_url, get_list_items, profile_url, self_url, get_cp_codes
from akamai.edgegrid import EdgeGridAuth, EdgeRc
from django.db.models import Q
import requests
import json
import traceback
import pandas as pd
import re
import logging
from datetime import datetime
from user.variables import edgerc, section
from .helpers import find_matches
from django.forms.models import model_to_dict
from django.utils.safestring import mark_safe
from client.models import UserInput
from django.core.exceptions import ValidationError
from django.db import DatabaseError


# Configure logging
logger = logging.getLogger(__name__)

def print_clients_and_clientlists():
    # Print all Client objects
    print("Client objects:")
    for client in Client.objects.all():
        print(
            f"ID: {client.id}, Name: {client.name}, SwitchKey: {client.key}"
        )

    # Print all ClientList objects
    print("\nClientList objects:")
    for client_list in ClientList.objects.all():
        client_name = client_list.client.name if client_list.client else 'None'
        print(
            f"ID: {client_list.id}, ClientList Name: {client_list.name}, listId: {client_list.listId}, SwitchKey: {client_list.key}")


def populate_data_from_api():
    s = requests.Session()
    s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
    if not APIUser.objects.exists():
        apiUser = APIUser()
        apiUser.update_profile(s=s, profile_url=profile_url, self_url=self_url)
        apiUser.save()
        print(apiUser.name)
        print(apiUser.email)
        print(apiUser.accountSwitch)
    if not Client.objects.exists():
        keys = s.get(switch_keys_url)
        client_details = keys.json()

        for client_data in client_details:
            switch_key = client_data.get('accountSwitchKey')
            querystring = {
                "actions": True,
                "authGrants": True,
                "notifications": True,
                "accountSwitchKey": switch_key
            }
            cp_codes = s.get(get_cp_codes, params=querystring).json()
            client, created = Client.objects.get_or_create(
                key=client_data.get('accountSwitchKey'),
                cpcodes=cp_codes,
                defaults={'name': client_data.get('accountName')}
            )



            try:
                client_lists = s.get(accessible_client_lists_url, params=querystring).json()

                for each in client_lists['content']:
                    try:
                        # Save ClientList instance first
                        client_list, created = ClientList.objects.update_or_create(
                            listId=each.get('listId'),
                            defaults={
                                'key': client_data.get('accountSwitchKey'),
                                'client': client,
                                'name': each.get('name'),
                                'updatedBy': each.get('updatedBy'),
                                'productionActivationStatus': each.get('productionActivationStatus'),
                                'createDate': each.get('createDate'),
                                'updateDate': each.get('updateDate'),
                                'itemsCount': each.get('itemsCount'),
                                'notes': each.get('notes')
                            }
                        )

                        # Example of adding items to the many-to-many relationship
                        items = each.get('items', [])
                        for item in items:
                            item_instance, created = ClientListItems.objects.get_or_create(
                                value=item['value'],
                                defaults={
                                    'description': item.get('description', ''),
                                    'stagingStatus': item.get('stagingStatus', ''),
                                    'productionStatus': item.get('productionStatus', ''),
                                    'createDate': item.get('createDate', None),
                                    'updateDate': item.get('updateDate', None),
                                    'createdBy': item.get('createdBy', ''),
                                    'updatedBy': item.get('updatedBy', '')
                                }
                            )
                            item_instance.save()  # Save the item instance
                            client_list.items.add(item_instance)  # Add item to the many-to-many relationship

                    except IntegrityError as e:
                        print(f"Failed to insert/update client list with listId: {each.get('listId')}. Error: {e}")

            except (Exception, KeyError) as e:
                error_message = str(e)
                print(f"Error occurred while fetching the client lists of {client}: {error_message}")

                # In case of error, create a ClientList object with the error message in notes
                ClientList.objects.create(
                    client=client,
                    notes=error_message
                )

    #print_clients_and_clientlists()

def populate_data_from_api_SE():
    s = requests.Session()
    s.auth = EdgeGridAuth.from_edgerc(edgerc, section)
    if not APIUser.objects.exists():
        apiUser = APIUser()
        apiUser.update_profile(s=s, profile_url=profile_url, self_url=self_url)
        apiUser.save()
        print(apiUser.name)
        print(apiUser.email)
        print(apiUser.accountSwitch)
    if not Client.objects.exists():
        # keys = s.get(switch_keys_url)
        # client_details = keys.json()
        #
        # for client_data in client_details:
        #     client, created = Client.objects.get_or_create(
        #         #key=client_data.get('accountSwitchKey'),
        #         defaults={'name': client_data.get('accountName')}
        #     )
        #     # The switch key is not required in the SE environment
        #     # switch_key = client_data.get('accountSwitchKey')
        querystring = {
            "actions": True,
            "authGrants": True,
            "notifications": True
        }
        if "@non.se.com" in str(apiUser.email):
            try:
                SE = "Schneider Electric"
                global client
                client, created = Client.objects.get_or_create(
                         defaults={'name': ("%s" % SE)}
                     )
                logger.info(f"Created/Updated new Client Model for {client.name}")
                print(f"Created/Updated new Client Model for {client.name}")
            except Exception as e:
                print(f"Failed to insert/update client with name: {SE}. Error: {e}")
                logger.error(f"Failed to insert/update client with name: {SE}. Error: {e}")
                traceback.print_exc()
        else:
            logger.error("Non SE account detected. Exiting.")
            exit()

        try:
            client_lists = s.get(accessible_client_lists_url, params=querystring).json()

            for each in client_lists['content']:
                try:
                    # Save ClientList instance first
                    client_list, created = ClientList.objects.update_or_create(
                        listId=each.get('listId'),
                        defaults={
                            #'key': client_data.get('accountSwitchKey'),
                            'client': client,
                            'name': each.get('name'),
                            'updatedBy': each.get('updatedBy'),
                            'productionActivationStatus': each.get('productionActivationStatus'),
                            'createDate': each.get('createDate'),
                            'updateDate': each.get('updateDate'),
                            'itemsCount': each.get('itemsCount'),
                            'notes': each.get('notes')
                        }
                    )

                    # Example of adding items to the many-to-many relationship
                    items = each.get('items', [])
                    for item in items:
                        item_instance, created = ClientListItems.objects.get_or_create(
                            value=item['value'],
                            defaults={
                                'description': item.get('description', ''),
                                'stagingStatus': item.get('stagingStatus', ''),
                                'productionStatus': item.get('productionStatus', ''),
                                'createDate': item.get('createDate', None),
                                'updateDate': item.get('updateDate', None),
                                'createdBy': item.get('createdBy', ''),
                                'updatedBy': item.get('updatedBy', '')
                            }
                        )
                        item_instance.save()  # Save the item instance
                        client_list.items.add(item_instance)  # Add item to the many-to-many relationship

                except IntegrityError as e:
                    print(f"Failed to insert/update client list with listId: {each.get('listId')}. Error: {e}")

        except (Exception, KeyError) as e:
            error_message = str(e)
            print(f"Error occurred while fetching the client lists of {client}: {error_message}")

            # In case of error, create a ClientList object with the error message in notes
            ClientList.objects.create(
                client=client,
                notes=error_message
            )

    #print_clients_and_clientlists()

def refresh_data(request):
    Client.objects.all().delete()
    ClientList.objects.all().delete()
    ClientListItems.objects.all().delete()
    APIUser.objects.all().delete()
    populate_data_from_api()
    form = ClientForm()
    apiUser = APIUser()
    # print("Creating User")
    # apiUser.save()
    # print(apiUser.name)
    # print(apiUser.email)
    # print(apiUser.accountSwitch)
    context = {'apiUser': apiUser, 'form': form }
    return render(request, 'home3.html', context=context )


def home(request):
    if request.method == 'POST':
        clientform = ClientForm(request.POST)
        items_form = ItemsForm(request.POST)
        if clientform.is_valid():
            listofclients = clientform.cleaned_data['listofclients']
    else:
        populate_data_from_api()
        clientform = ClientForm()
        items_form = ItemsForm()

    if APIUser.objects.exists():
        print("There are APIUser objects in the database.")
        latest_user = APIUser.objects.order_by('-id').first()  # Fetches the last object based on descending id order
        print(latest_user.name)
        print(latest_user.email)
        print(latest_user.accountSwitch)
    else:
        print("No APIUser objects found.")


    context = {'apiUser': latest_user, 'form': clientform, 'items_form': items_form}
    return render(request, 'home2.html', context=context)




def load_client_lists(request):
    switchKey = request.GET.get('client')
    print("The switchKey is :",switchKey)
    clientlists = ClientList.objects.filter(key=switchKey).order_by('name')
    print("The client lists are : ",clientlists)
    return render(request, 'client_list_dropdown_list_options.html', {'client_lists': clientlists})

def display_selection(request):
    client_list_id = request.GET.get('client_list')
    client_list = ClientList.objects.get(pk=client_list_id)
    return render(request, 'display_selection.html', {'client_list': client_list})

def load_blacklists(request):
    client_list = request.GET.get('client_list')
    print("The selected clientlist is :",client_list)
    return render(request, 'blacklists.html', {})


def ip_address_view(request):
    if request.method == 'POST':
        items_form = ItemsForm(request.POST)
        if items_form.is_valid():
            # Process the form data here
            ip_addresses = items_form.cleaned_data['ip_list']
            return render(request, 'ip_submit_success.html', {'ip_addresses': ip_addresses})
    else:
        items_form = ItemsForm()

    return render(request, 'home3.html', {'items_form':items_form})

def get_list_id(id):
    # Get the ClientList object with the specified id
    client_list = get_object_or_404(ClientList, id=id)
    # Get the listId from the ClientList object
    list_id = client_list.listId
    return list_id

def populate_clientlist_items(request):
    selected_option = request.GET.get('client_list')
    client_listid = get_list_id(selected_option)
    # Get the ClientList object with the specified id
    logger.info(f"Function triggered for {client_listid}")
    print(f"populate_clientlist_items Function triggered for {client_listid}")
    client_list = get_object_or_404(ClientList, id=selected_option)
    for attr, value in client_list.__dict__.items():
        print(f"{attr}: {value}")
    # Construct the API URL using the client_list_id
    api_url = get_list_items.replace("<listId>", client_list.listId)  # Replace with the actual URL
    querystring = {
        "actions": True,
        "authGrants": True,
        "notifications": True,
        "accountSwitchKey": client_list.key
    }

    # Make the API call to retrieve data
    s = requests.Session()
    s.auth = EdgeGridAuth.from_edgerc(edgerc, section)  # Ensure authentication is set up correctly
    response = s.get(api_url, params=querystring)

    if response.status_code == 200:
        # Parse the JSON response
        client_list_items = response.json()

        for item in client_list_items['content']:
            try:
                # Populate the ClientListItems objects
                item_instance, created = ClientListItems.objects.update_or_create(
                    value=item['value'],
                    defaults={
                        'description': item.get('description', ''),
                        'stagingStatus': item.get('stagingStatus', ''),
                        'productionStatus': item.get('productionStatus', ''),
                        'createDate': item.get('createDate', None),
                        'updateDate': item.get('updateDate', None),
                        'expirationDate': item.get('expirationDate', None),
                        'createdBy': item.get('createdBy', ''),
                        'updatedBy': item.get('updatedBy', ''),
                        'type': item.get('type', ''),
                        'tags': item.get('tags', '')
                    }
                )
                item_instance.client_lists.add(client_list)
                logger.info(f"Saved item: {item_instance.value}")
                print(f"Saved item: {item_instance.value}")
                #print(f"Client List Type: {type(item_instance.client_lists)}")
            except Exception as e:
                logger.error(f"Error saving item {item['value']}: {e}")
                print(f"Error saving item {item['value']}: {e}")

        logger.info("Client list items populated successfully!")
        print("Client list items populated successfully!")
    else:
        print(f"Failed to retrieve data from the API: {response.status_code}")
    return render(request, 'display_selection.html', {'client_list': client_list})


def items_form_page(request):
    print("This is the items_form_page function\nRequest from previous form is :", request)
    api_response = None
    items_form = ItemsForm(request.POST or None)
    switch_key = request.GET.get('client')
    client_list_id = request.GET.get('client_list')
    print("client_list_identifier : ",client_list_id)
    print("Switch Key : ",switch_key)
    listId = get_list_id(client_list_id)
    #populate_client_list_items(client_list_id)
    return render(request, 'ip_form_page.html',{'items_form': items_form, 'api_response': api_response, 'client_list': client_list_id})


def check_results_for_input(request):
    print("check_results_for_input called. Request data:", request.POST)

    if request.method == "POST":
        # Get input data from POST request
        items = request.POST.get('items', '')
        client_list_id = request.POST.get('client_list_id')
        user_input_list = []

        # Validate client_list_id
        try:
            switch_key = ClientList.objects.get(id=client_list_id)
        except ClientList.DoesNotExist:
            return HttpResponse("Invalid client list ID. Please provide a valid one.", status=400)

        print("Received items:", items)
        print("Type of items:", type(items))

        # Process user input
        if items:
            # Split and clean user input
            entries = re.split(r'[,\n;]+', items)
            user_input_list = [entry.strip() for entry in entries if
                          entry.strip()]  # Remove empty or whitespace-only entries

            # Update or create UserInput objects
            for item in user_input_list:
                UserInput.objects.update_or_create(input_text=item)

            # Retrieve all UserInput and ClientListItems objects
            all_user_inputs = UserInput.objects.all()
            matches = ClientListItems.objects.filter(value__in=user_input_list)

            # Convert matches to a DataFrame
            #matches_as_dicts = [model_to_dict(match) for match in matches]
            matches_as_dicts = [
                {
                    **model_to_dict(match),
                    "client_lists": [
                        str(client_list) for client_list in match.client_lists.all()
                    ] if hasattr(match, 'client_lists') else None,
                }
                for match in matches
            ]

            #print(type(matches_as_dicts['client_lists']))
            df = pd.DataFrame.from_records(matches_as_dicts)

            print("The resulting dataframe is :")
            print(df)

            def highlight_cells(val, items=None):
                # Ensure 'items' is a set of hashable elements
                items = set(items) if items else set()

                # Handle related objects or complex data
                if hasattr(val, '_meta'):  # Check if it's a Django model instance
                    # Use a meaningful representation, like the primary key or a string
                    val = str(val)  # Or val.pk for primary key

                # Handle cases where 'val' is a list
                if isinstance(val, list):
                    # Convert each element to a hashable form, e.g., a tuple
                    val = tuple(
                        str(element) if hasattr(element, '_meta') else element
                        for element in val
                    )

                # Check if the value is in the set
                if val in items:
                    return 'background-color: yellow'  # Highlight with yellow
                return ''

            # Ensure items_list is a set for performance
            user_input_set = set(user_input_list)

            # Create a mapping for user input
            user_input_mapping = {item: item for item in user_input_list}

            # Add the new column to the DataFrame where matches with the 'value' field are found
            df['UserInput'] = df['value'].map(user_input_mapping).fillna('')  # Fill empty cells with ''
            # Reorder the columns to place "UserInput" as the first column
            columns = ['UserInput'] + [col for col in df.columns if col != 'UserInput']
            df = df[columns]  # Rearrange columns in the desired order
            # Get columns and rows from the DataFrame
            columns = df.columns.tolist()
            rows = df.to_dict('records')
            # Apply highlighting to the DataFrame
            if not df.empty:
                # Preprocess the DataFrame for styling (convert relational objects, if necessary)
                for col in df.columns:
                    df[col] = df[col].map(lambda x: str(x) if hasattr(x, '_meta') else x)

                # Apply the highlight function
                styled_df = df.style.map(lambda val: highlight_cells(val, user_input_set))

                # Convert styled DataFrame to HTML
                styled_html = styled_df.to_html()
            else:
                styled_html = '<p>No matches found.</p>'
            # Prepare data for the "UserInput" column
            user_input_rows = [
                {"user_input": row['UserInput'],
                 "highlight_class": "highlight" if row['UserInput'] in user_input_set else ""}
                for _, row in df.iterrows()
            ]

            # Prepare data for the rest of the DataFrame
            main_columns = [col for col in df.columns if col != "UserInput"]
            main_rows = []
            for _, row in df.drop(columns=["UserInput"]).iterrows():
                main_rows.append([
                    {
                        "value": cell,
                        "highlight_class": "highlight" if (
                                isinstance(cell, list) and any(item in user_input_set for item in cell)  # Handle lists
                                or not isinstance(cell, list) and cell in user_input_set  # Handle hashable types
                        ) else ""
                    }
                    for cell in row
                ])
            # Render the output template
            return render(request,'output.html', {
                'user_inputs': all_user_inputs,
                'styled_html': mark_safe(styled_html),
                "user_input_rows": user_input_rows,
                "main_columns": main_columns,
                "main_rows": main_rows,
                'matches':matches_as_dicts,
                'matchesdf':mark_safe(df.to_html()),
                'highlights':user_input_list,
                "columns": columns,
                "rows": rows
            })

    # If not a POST request or no items provided
    return HttpResponse("Invalid request or no items provided.", status=400)

def parse_and_populate_client_list_items(data, clientListId):
    content = data.get('content', [])
    for item in content:
        # Extract the relevant fields from the item
        client_list_data = {
            'value': item['value'],
            'listId': clientListId,  # Assuming 'value' corresponds to 'listId'
            'updatedBy': item['updatedBy'],
            'productionActivationStatus': item['productionStatus'],
            'createDate': datetime.strptime(item['createDate'], '%Y-%m-%dT%H:%M:%S.%f%z'),
            'updateDate': datetime.strptime(item['updateDate'], '%Y-%m-%dT%H:%M:%S.%f%z'),
            #'itemsCount': data['itemsCount'],  # Using totalElements for itemsCount
            'notes': item.get('tags', [])[0] if item.get('tags') else '',
            # Assuming 'notes' corresponds to the first tag
            'key': item['value']  # Using 'value' as the key
        }

        # Get or create the associated Client instance (replace 'client_key' with the actual key)
        client_key = item['createdBy']  # Assuming 'createdBy' corresponds to the client key
        client, created = Client.objects.get_or_create(key=client_key, defaults={'name': item['description']})

        # Create or update the ClientList instance
        client_list, created = ClientList.objects.update_or_create(
            listId=client_list_data['listId'],
            defaults={**client_list_data, 'client': client}
        )

    return

def success_view(request):
    return render(request, 'success.html')
