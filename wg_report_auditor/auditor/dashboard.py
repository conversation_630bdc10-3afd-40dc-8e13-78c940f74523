import calendar
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from django.db.models import Count, Q
from django.db.models.functions import TruncMonth
from django.utils import timezone
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from .models import AuditResults

def categorize_errors(audit_result):
    """Categorize errors based on audit result fields"""
    categories = []
    
    if audit_result.missing_values:
        categories.append("Missing Values")
    
    if audit_result.structural_issues:
        categories.append("Structural Issues")
    
    if audit_result.blacklisted_ips:
        categories.append("Blacklisted IPs")
        
    # Extract more specific categories from comments if available
    if audit_result.structure_comments:
        if "Orange reference is missing" in audit_result.structure_comments:
            categories.append("Missing Reference")
        if "validation requirements" in audit_result.structure_comments:
            categories.append("Validation Failure")
    
    # If no categories were found but action is required, add generic category
    if not categories and audit_result.action_required:
        categories.append("Other Issues")
        
    return categories

@login_required
def dashboard(request):
    """Main dashboard view showing audit statistics"""
    # Get date range (default: last 12 months)
    months = int(request.GET.get('months', 12))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=30*months)
    
    # Base queryset - filter by ticket_date instead of created_at
    queryset = AuditResults.objects.filter(ticket_date__gte=start_date)
    
    # 1. Defective files per month (action_required=True)
    defective_by_month = (
        queryset.filter(action_required=True)
        .annotate(month=TruncMonth('ticket_date'))
        .values('month')
        .annotate(count=Count('id'))
        .order_by('month')
    )
    
    # Also get total files per month for comparison
    total_by_month = (
        queryset
        .annotate(month=TruncMonth('ticket_date'))
        .values('month')
        .annotate(count=Count('id'))
        .order_by('month')
    )
    
    # Prepare data for monthly chart
    months_labels = []
    defective_counts = []
    total_counts = []
    
    # Create a dict for quick lookup
    defective_dict = {item['month']: item['count'] for item in defective_by_month}
    total_dict = {item['month']: item['count'] for item in total_by_month}
    
    # Fill in all months in range
    current = timezone.make_aware(datetime(start_date.year, start_date.month, 1))
    while current <= end_date:
        month_label = calendar.month_name[current.month] + " " + str(current.year)
        months_labels.append(month_label)
        
        defective_counts.append(defective_dict.get(current.date(), 0))
        total_counts.append(total_dict.get(current.date(), 0))
        
        # Move to next month
        if current.month == 12:
            current = timezone.make_aware(datetime(current.year+1, 1, 1))
        else:
            current = timezone.make_aware(datetime(current.year, current.month+1, 1))
    
    # 2. Types of errors (categorized)
    error_categories = Counter()
    
    # Get all action_required results
    action_required_results = queryset.filter(action_required=True)
    
    # Categorize each result
    for result in action_required_results:
        categories = categorize_errors(result)
        for category in categories:
            error_categories[category] += 1
    
    # 3. Errors by customer
    customer_errors = (
        queryset.filter(action_required=True)
        .values('customer_name')
        .annotate(count=Count('id'))
        .order_by('-count')
    )
    
    # 4. Other relevant statistics
    total_files = queryset.count()
    defective_files = queryset.filter(action_required=True).count()
    defect_rate = (defective_files / total_files * 100) if total_files > 0 else 0
    
    # Recent issues (last 7 days)
    recent_date = timezone.now() - timedelta(days=7)
    recent_issues = queryset.filter(
        action_required=True, 
        ticket_date__gte=recent_date
    ).count()
    
    # Generate Plotly charts
    # Monthly trend chart
    monthly_fig = go.Figure()
    monthly_fig.add_trace(go.Bar(
        x=months_labels,
        y=defective_counts,
        name='Defective Files',
        marker_color='#ff6600'
    ))
    monthly_fig.add_trace(go.Bar(
        x=months_labels,
        y=total_counts,
        name='Total Files',
        marker_color='#cccccc'
    ))
    monthly_fig.update_layout(
        title='Files Processed vs. Defective Files by Month',
        xaxis_title='Month',
        yaxis_title='Number of Files',
        barmode='group'
    )
    monthly_chart = monthly_fig.to_html(full_html=False)
    
    # Error categories pie chart
    if error_categories:
        categories_df = pd.DataFrame({
            'Category': list(error_categories.keys()),
            'Count': list(error_categories.values())
        })
        categories_fig = px.pie(
            categories_df, 
            values='Count', 
            names='Category',
            title='Error Categories Distribution'
        )
        categories_fig.update_traces(marker=dict(colors=px.colors.qualitative.Set3))
        categories_chart = categories_fig.to_html(full_html=False)
    else:
        categories_chart = "<p>No error categories data available</p>"
    
    # Customer errors bar chart
    if customer_errors:
        # Limit to top 10 customers for readability
        top_customers = list(customer_errors)[:10]
        customer_fig = px.bar(
            x=[c['customer_name'] for c in top_customers],
            y=[c['count'] for c in top_customers],
            labels={'x': 'Customer', 'y': 'Number of Defective Files'},
            title='Top 10 Customers by Defective Files'
        )
        customer_fig.update_layout(xaxis_tickangle=-45)
        customer_chart = customer_fig.to_html(full_html=False)
    else:
        customer_chart = "<p>No customer error data available</p>"
    
    # Prepare context for template
    context = {
        'total_files': total_files,
        'defective_files': defective_files,
        'defect_rate': round(defect_rate, 2),
        'recent_issues': recent_issues,
        'monthly_chart': monthly_chart,
        'categories_chart': categories_chart,
        'customer_chart': customer_chart,
        'months': months,
    }
    
    return render(request, 'dashboard.html', context)