from datetime import datetime
from django.db import models
import re
from django.core.validators import RegexValidator, validate_ipv46_address
from loguru import logger
from django.utils import timezone
import ast
from .verification import check_blacklist_entry
import ipaddress
import json
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings

# Create your models here.

# Regex for validating Fully Qualified Domain Names (FQDNs)
FQDN_REGEX = re.compile(
    r"^(?=.{1,255}$)(?!-)[A-Za-z0-9-]+(\.[A-Za-z0-9-]+)*\.[A-Za-z]{2,}$"
)

class AuditResults(models.Model):
    """
    Model for storing audit results and related information.
    
    This model tracks ticket information, customer details, remediation status,
    and various comments and findings from security audits. It also handles
    the extraction and validation of IP addresses and domain names.
    """
    # Ticket and customer information
    ticket = models.CharField(
        max_length=15, 
        unique=True,
        validators=[RegexValidator(regex=r'^[A-Za-z0-9]+$', message='Ticket must be alphanumeric')],
        help_text="Unique alphanumeric ticket identifier"
    )
    original_filename = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Original filename of the report"
    )
    ticket_date = models.DateTimeField(
        blank=True, 
        null=True,
        help_text="Date and time when the ticket was created"
    )
    create_date = models.DateTimeField(
        auto_now_add=True,
        help_text="Date and time when this record was created"
    )
    customer_name = models.CharField(
        max_length=100,
        help_text="Name of the customer associated with this audit"
    )
    
    # Audit classification
    priority = models.CharField(
        max_length=2,
        null=True,
        help_text="Priority level of the audit (e.g., P1, P2)"
    )
    event_type = models.CharField(
        max_length=100,
        null=True,
        help_text="Type of security event being audited"
    )
    
    # Remediation information
    remediation = models.BooleanField(
        default=False,
        help_text="Whether remediation actions were taken"
    )
    remediation_action = models.CharField(
        max_length=1000, 
        blank=True, 
        null=True,
        help_text="Description of remediation actions taken"
    )
    remediation_reason = models.CharField(
        max_length=1000, 
        blank=True, 
        null=True,
        help_text="Reason for remediation actions"
    )
    
    # Audit findings and comments
    recommendations = models.TextField(  # Changed from CharField to TextField for longer content
        blank=True, 
        null=True,
        help_text="Recommendations for addressing security issues"
    )
    blacklist_comments = models.TextField(  # Changed from CharField to TextField
        blank=True, 
        null=True,
        help_text="Comments regarding blacklisted IPs"
    )
    structure_comments = models.TextField(  # Changed from CharField to TextField
        blank=True, 
        null=True,
        help_text="Comments regarding structural issues"
    )
    
    # Technical details
    blacklisted_ips = models.JSONField(
        blank=True, 
        null=True,
        default=list,  # Added default to avoid None issues
        help_text="List of blacklisted IP addresses"
    )
    destination_hostnames = models.JSONField(
        blank=True, 
        null=True,
        default=list,  # Added default to avoid None issues
        help_text="List of destination hostnames"
    )
    wsa_link = models.URLField(  # Changed from CharField to URLField for validation
        max_length=1000, 
        blank=True, 
        null=True,
        help_text="Link to Web Security Appliance details"
    )
    
    # Status flags
    missing_values = models.BooleanField(
        default=False,
        help_text="Flag indicating missing required values"
    )
    structural_issues = models.BooleanField(
        default=False,
        help_text="Flag indicating structural issues in the audit"
    )
    action_required = models.BooleanField(
        default=False,
        help_text="Flag indicating action is required due to issues"
    )
    action_history = models.JSONField(
        blank=True,
        null=True,
        default=list,
        help_text="History of changes to action_required status"
    )
    
    # Metadata
    created_at = models.DateTimeField(  # Added creation timestamp
        auto_now_add=True,
        help_text="Date and time when this record was created"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Date and time when this record was last updated"
    )

    class Meta:
        """
        Model metadata options.
        """
        ordering = ['-updated_at']
        verbose_name = "Audit Result"
        verbose_name_plural = "Audit Results"
        indexes = [
            models.Index(fields=['ticket']),
            models.Index(fields=['customer_name']),
            models.Index(fields=['updated_at']),
        ]

    def __str__(self):
        """
        String representation of the audit result.
        
        Returns:
            str: The ticket identifier
        """
        return f"{self.ticket}"

    def _extract_ips(self, raw_input):
        """
        Extracts valid IP addresses from raw input.

        Args:
            raw_input (str or list): Input containing IP addresses.

        Returns:
            list: A unique list of valid IP addresses.
        """
        logger.info("Extracting IP addresses")
        ip_addresses = set()

        # Handle None or empty input
        if raw_input is None or raw_input == [] or raw_input == "":
            logger.info("Empty input provided to _extract_ips")
            return []

        try:
            # Handle list or string-represented list
            if isinstance(raw_input, list):
                raw_input = " ".join(map(str, raw_input))
            elif isinstance(raw_input, str) and raw_input.startswith("["):
                try:
                    # Try json.loads first (more reliable)
                    try:
                        parsed_list = json.loads(raw_input)
                        if isinstance(parsed_list, list):
                            raw_input = " ".join(map(str, parsed_list))
                    except json.JSONDecodeError:
                        # Fall back to ast.literal_eval
                        raw_input = " ".join(map(str, ast.literal_eval(raw_input)))
                except (ValueError, SyntaxError) as e:
                    logger.error(f"Failed to parse list-like string: {e}")
                    # Don't return empty list here, try to extract IPs from the raw string

            # Extract all valid IP addresses
            for ip in re.findall(r"\b(?:\d{1,3}\.){3}\d{1,3}\b", raw_input):
                try:
                    # Validate the IP address
                    ipaddress.ip_address(ip)
                    ip_addresses.add(ip)
                except ValueError:
                    logger.warning(f"Invalid IP skipped: {ip}")

        except Exception as e:
            logger.error(f"Failed to process IP addresses: {str(e)}")
            # Don't return empty list on error, return what we've found so far

        return list(ip_addresses)

    def _extract_fqdns(self, raw_input):
        """
        Extracts valid Fully Qualified Domain Names (FQDNs) from raw input.

        Args:
            raw_input (str or list): Input containing hostnames.

        Returns:
            list: A unique list of valid FQDNs.
        """
        logger.info("Extracting FQDNs")
        hostnames = set()

        if not raw_input:
            return []

        try:
            # Handle list or string-represented list
            if isinstance(raw_input, list):
                raw_input = ",".join(map(str, raw_input))
            elif isinstance(raw_input, str) and raw_input.startswith("["):
                try:
                    raw_input = ",".join(map(str, ast.literal_eval(raw_input)))
                except (ValueError, SyntaxError) as e:
                    logger.error(f"Failed to parse list-like string: {e}")
                    return []

            # Extract valid FQDNs
            for hostname in re.findall(r"[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}", raw_input):
                if FQDN_REGEX.fullmatch(hostname):
                    hostnames.add(hostname)
                    logger.debug(f"Valid FQDN found: {hostname}")
                else:
                    logger.debug(f"Invalid FQDN skipped: {hostname}")

        except Exception as e:
            logger.error(f"Failed to process FQDNs: {str(e)}")

        return list(hostnames)

    def save(self, *args, **kwargs):
        """
        Overrides save() to automatically extract and store valid IPs and FQDNs.
        Also updates action_required based on missing_values and structural_issues.
        Sends email notifications when action_required changes to True.
        
        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        # Extract user info if provided
        user = kwargs.pop('user', None)
        comment = kwargs.pop('comment', '')
        
        # Check if action_required should be automatically set
        if self.missing_values or self.structural_issues:
            self.action_required = True
        
        # Check if this is an update or a new instance
        action_required_changed = False
        if self.pk:  # This is an update
            try:
                old_instance = AuditResults.objects.get(pk=self.pk)
                
                # Update action history if action_required changed and user is provided
                if user and old_instance.action_required != self.action_required:
                    if self.action_history is None:
                        self.action_history = []
                    
                    self.action_history.append({
                        'timestamp': timezone.now().isoformat(),
                        'user': user.username if hasattr(user, 'username') else str(user),
                        'action': 'enabled' if self.action_required else 'disabled',
                        'comment': comment
                    })
                    logger.info(f"Action history updated for ticket {self.ticket} by {user.username if hasattr(user, 'username') else str(user)}")
                
                action_required_changed = old_instance.action_required != self.action_required
            except AuditResults.DoesNotExist:
                action_required_changed = self.action_required
        else:
            action_required_changed = self.action_required
        
        # Call the parent save method first
        super().save(*args, **kwargs)
        
        # Process IPs and FQDNs
        try:
            # Extract IPs without checking blacklist
            extracted_ips = self._extract_ips(self.blacklisted_ips)
            if extracted_ips != self.blacklisted_ips:
                self.blacklisted_ips = extracted_ips
                logger.info(f"Updated extracted IPs: {len(self.blacklisted_ips)} addresses")
                # Save again to store processed IPs
                super().save(update_fields=['blacklisted_ips'])
        except Exception as e:
            logger.error(f"Error processing IP addresses: {str(e)}")
            self.blacklisted_ips = []
        
        try:
            extracted_fqdns = self._extract_fqdns(self.destination_hostnames)
            if extracted_fqdns != self.destination_hostnames:
                self.destination_hostnames = extracted_fqdns
                logger.info(f"Updated extracted hostnames: {len(self.destination_hostnames)} hostnames")
                # Save again to store processed hostnames
                super().save(update_fields=['destination_hostnames'])
        except Exception as e:
            logger.error(f"Error processing hostnames: {str(e)}")
            self.destination_hostnames = []
        
        # Now check blacklist for IPs - this will save the model if comments are updated
        self._check_blacklist_for_ips()
        
        # Special handling for placeholder tickets to ensure structure comments are set
        if self.ticket.startswith("NOID") and self.structural_issues:
            # Make sure structure comments exist for placeholder tickets
            if not self.structure_comments or "PLACEHOLDER TICKET ID" not in self.structure_comments:
                from .structure_check import update_structure_comments
                from .utils import format_placeholder_comment
                
                # Use the centralized function to format the comment
                comment = format_placeholder_comment(
                    self.ticket, 
                    self.original_filename or 'Unknown', 
                    "The Orange reference is missing or does not meet validation requirements."
                )
                
                update_structure_comments(self.ticket, comment)
                # Refresh from database to get updated structure_comments
                refreshed = AuditResults.objects.get(pk=self.pk)
                self.structure_comments = refreshed.structure_comments
        
        # Send email notification if action_required was changed to True - moved to the end
        if action_required_changed and self.action_required:
            self._send_action_required_email()
        
        logger.info(f"AuditResults saved successfully for ticket: {self.ticket}")

    def _check_blacklist_for_ips(self):
        """
        Check blacklist status for all IPs in blacklisted_ips.
        This is separated from extraction to avoid duplicate checks.
        """
        from .utils import update_blacklist_comments
        from .verification import check_blacklist_entry
        
        if not self.blacklisted_ips:
            logger.info("No IPs to check against blacklist")
            return
        
        # Track if we've made any changes
        comments_updated = False
        
        # Check blacklist for each IP
        for ip in self.blacklisted_ips:
            logger.info(f"Checking blacklist status of {ip}")
            comment = check_blacklist_entry(ip, self.customer_name)
            
            # Update comments
            if update_blacklist_comments(self, ip, comment):
                comments_updated = True
        
        # Save the model if comments were updated
        if comments_updated:
            logger.info(f"Saving updated blacklist comments for ticket {self.ticket}")
            # Use update_fields to only update the blacklist_comments field
            super().save(update_fields=['blacklist_comments'])
            return True
        else:
            logger.info(f"No new blacklist comments to save for ticket {self.ticket}")
            return False

    def _get_customer_short_name(self, max_length=8):
        """
        Creates a short form of customer name for email subjects.
        
        Args:
            max_length (int): Maximum length for customer name before shortening
        
        Returns:
            str: Original name if short enough, otherwise abbreviated version
        """
        if not self.customer_name or len(self.customer_name) <= max_length:
            return self.customer_name
        
        # For hyphenated names, use initials of each part
        if '-' in self.customer_name:
            parts = self.customer_name.split('-')
            return ''.join(part[0] for part in parts if part)
        
        # For space-separated names, use initials
        if ' ' in self.customer_name:
            parts = self.customer_name.split()
            return ''.join(part[0] for part in parts if part)
        
        # For single long words, use first 2 and last 2 characters
        if len(self.customer_name) > max_length:
            return f"{self.customer_name[:2]}{self.customer_name[-2:]}"
        
        return self.customer_name

    def _send_action_required_email(self):
        """
        Sends an email notification when action is required.
        Only attempts to send if email credentials are properly configured.
        """
        # Clean up any quotes in the credentials for checking
        host_user = settings.EMAIL_HOST_USER.strip("'\"").strip()
        host_password = settings.EMAIL_HOST_PASSWORD.strip("'\"").strip()
        
        # Print the raw values for debugging
        print(f"DEBUG - Email credentials: HOST_USER='{settings.EMAIL_HOST_USER}' (len={len(settings.EMAIL_HOST_USER)}), cleaned='{host_user}' (len={len(host_user)}), PASSWORD_LEN={len(settings.EMAIL_HOST_PASSWORD)}, cleaned_len={len(host_password)}")
        
        # First check if email credentials are configured and not empty strings
        if not host_user or not host_password:
            message = "Email sending skipped: Email credentials are empty or contain only quotes"
            print(message)
            logger.warning(message)
            return
        
        try:
            # Prepare context for templates
            context = {
                'audit': self
            }
            
            # Get short form of customer name for subject
            short_name = self._get_customer_short_name()
            
            # Render email templates
            subject = f"Action Required: {self.ticket} - {short_name}"
            text_content = render_to_string('emails/action_required_notification.txt', context)
            html_content = render_to_string('emails/action_required_notification.html', context)
            
            # Get admin recipients
            admin_recipients = [email for name, email in settings.ADMINS]
            
            # Get APIUser recipients
            from user.models import APIUser
            api_user_recipients = list(APIUser.objects.filter(email__isnull=False).values_list('email', flat=True).distinct())
            
            # Combine recipients
            all_recipients = admin_recipients + api_user_recipients
            
            # If no recipients, log and return
            if not all_recipients:
                message = "Email sending skipped: No recipients configured"
                print(message)
                logger.warning(message)
                return
            
            # Clean up the from_email as well
            from_email = settings.DEFAULT_FROM_EMAIL.strip("'\"").strip() or host_user
            
            # Log email configuration and recipients
            logger.info(f"Email configuration: HOST={settings.EMAIL_HOST}, PORT={settings.EMAIL_PORT}, TLS={settings.EMAIL_USE_TLS}")
            logger.info(f"Sending from: '{from_email}'")
            logger.info(f"Recipients ({len(all_recipients)}): {', '.join(all_recipients)}")
            
            # Send email to all recipients
            success_count = 0
            for recipient in all_recipients:
                try:
                    # Use Django's send_mail for simplicity
                    send_mail(
                        subject=subject,
                        message=text_content,
                        from_email=from_email,
                        recipient_list=[recipient],
                        fail_silently=False,
                        html_message=html_content
                    )
                    
                    logger.info(f"Email successfully sent to {recipient}")
                    success_count += 1
                except Exception as e:
                    logger.error(f"Failed to send to {recipient}: {str(e)}")
            
            logger.info(f"Email sending complete: {success_count}/{len(all_recipients)} successful")
            
        except Exception as e:
            logger.error(f"Overall email process failed: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

################
##############
