from django import forms
from django.forms.widgets import SelectMultiple

class FieldSelectionForm(forms.Form):
    """Form for selecting which fields to display in the results table"""
    fields = forms.MultipleChoiceField(
        choices=[
            ('ticket', 'Ticket ID'),
            ('customer_name', 'Customer Name'),
            ('date', 'Date'),
            ('priority', 'Priority'),
            ('event_type', 'Event Type'),
            ('destination_hostnames', 'Destination Hostnames'),
            ('blacklisted_ips', 'Blacklisted IPs'),
            ('remediation', 'Remediation'),
            ('remediation_action', 'Remediation Action'),
            ('remediation_reason', 'Remediation Reason'),
            ('wsa_link', 'WSA Link'),
            ('recommendations', 'Recommendations'),
            ('missing_values', 'Missing Values'),
            ('structural_issues', 'Structural Issues'),
        ],
        widget=SelectMultiple(attrs={'class': 'form-select', 'size': '8'}),
        required=False,
        initial=['ticket', 'customer_name', 'date', 'priority']
    )
