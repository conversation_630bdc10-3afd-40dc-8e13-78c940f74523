"""
Custom widgets for the auditor app.
This file provides proper imports for widgets that might be used in the app.
"""

from django.forms.widgets import (
    SelectMultiple,
    Select,
    CheckboxInput,
    TextInput,
    Textarea,
    DateInput,
    URLInput,
    NumberInput,
    EmailInput,
    PasswordInput,
    HiddenInput,
    FileInput,
)

# Re-export the widgets to make them available from this module
__all__ = [
    'SelectMultiple',
    'Select',
    'CheckboxInput',
    'TextInput',
    'Textarea',
    'DateInput',
    'URLInput',
    'NumberInput',
    'EmailInput',
    'PasswordInput',
    'HiddenInput',
    'FileInput',
]