# Dictionary with the Client Name and the listID of the blacklist that they use
ocd_blacklist_clientlists = {
    #"SCHNEIDER-ELECTRIC":"181331_TEST", # For TEST
    #"SCHNEIDER-ELECTRIC":"196521_SCHNEIDERIPBLACKLIST", # For PROD
    # "RIDF-ISS":"219488_BLOCKLISTIPISS",
    # "RIDF-SI":"219487_BLOCKLISTIPSI",
    # "DGFIP":"181061_DGFIPIPBLOCKLIST",
    # "ADN":"",
    # "Klesia":"",
    # "":""
}


'''
    "CUSTOMER_NAME (As it appears in the report": {
    "akamai_tenant": "Corresponding Akamai tenant",
    "switch_key": "Switch Key",
    "blacklist":"Blacklist (listid of blacklist used by CyberSOC to block IPs)"
    },
'''
REPORT_NAME_MAPPING = {
    "SCHNEIDER-ELECTRIC": {
    "akamai_tenant": None,
    "switch_key": None,
    "blacklist":"196521_SCHNEIDERIPBLACKLIST"
    },
    "RIDF-SI": {"field1": "value3", "field2": "value4","blacklist":"219487_BLOCKLISTIPSI"},
    "DGFIP": {"field1": "value5", "field2": "value6", "blacklist":"181061_DGFIPIPBLOCKLIST"}
}