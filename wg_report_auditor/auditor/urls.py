from django.urls import path
from django.contrib.auth.views import LogoutView
from . import views

app_name = 'auditor'

urlpatterns = [
    # Root URL redirect
    path('', views.root_redirect, name='root'),
    path('login/', views.login_view, name='login'),
    path('logout/', LogoutView.as_view(next_page='auditor:login'), name='logout'),
    path('upload/', views.upload_file, name='upload_file'),
    path('progress/', views.audit_progress, name='audit_progress'),
    path('results/', views.AuditResultsListView.as_view(), name='audit-results-list'),
    path('results/<int:pk>/', views.AuditResultDetailView.as_view(), name='audit-result-detail'),
    path('stop-audit/', views.stop_audit, name='stop_audit'),
    
    # Authentication related
    path('test-auth/', views.test_auth_page, name='test_auth_page'),
    path('auth-setup-instructions/', views.auth_setup_instructions, name='auth_setup_instructions'),
    path('admin-actions/', views.admin_actions, name='admin_actions'),
    path('clear-database/', views.clear_database, name='clear_database'),
    path('toggle-action-required/', views.toggle_action_required, name='toggle-action-required'),
    path('audit-history/<int:audit_id>/', views.get_audit_history, name='get-audit-history'),
]