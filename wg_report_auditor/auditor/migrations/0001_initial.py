# Generated by Django 4.2.20 on 2025-03-22 18:00

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AuditResults',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket', models.CharField(max_length=15, unique=True, validators=[django.core.validators.RegexValidator(message='Ticket must be alphanumeric', regex='^[A-Za-z0-9]+$')])),
                ('ticket_date', models.DateTimeField(blank=True, null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('customer_name', models.CharField(max_length=100)),
                ('priority', models.CharField(max_length=2)),
                ('event_type', models.Char<PERSON>ield(max_length=100)),
                ('remediation', models.BooleanField(default=False)),
                ('remediation_action', models.CharField(blank=True, max_length=1000, null=True)),
                ('remediation_reason', models.CharField(blank=True, max_length=1000, null=True)),
                ('blacklisted_ips', models.JSONField(blank=True, null=True)),
                ('wsa_link', models.CharField(blank=True, max_length=1000, null=True)),
                ('missing_values', models.BooleanField(default=False)),
                ('structural_issues', models.BooleanField(default=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
