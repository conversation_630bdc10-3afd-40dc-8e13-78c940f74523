# Generated by Django 5.1.6 on 2025-04-04 10:35

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('auditor', '0003_auditresults_destination_hostnames'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='auditresults',
            options={'ordering': ['-updated_at'], 'verbose_name': 'Audit Result',
                     'verbose_name_plural': 'Audit Results'},
        ),
        migrations.AddField(
            model_name='auditresults',
            name='blacklist_comments',
            field=models.TextField(blank=True, help_text='Comments regarding blacklisted IPs', null=True),
        ),
        migrations.AddField(
            model_name='auditresults',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now,
                                       help_text='Date and time when this record was created'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='auditresults',
            name='structure_comments',
            field=models.TextField(blank=True, help_text='Comments regarding structural issues', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='blacklisted_ips',
            field=models.JSONField(blank=True, default=list, help_text='List of blacklisted IP addresses', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='create_date',
            field=models.DateTimeField(auto_now_add=True, help_text='Date and time when this record was created'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='customer_name',
            field=models.CharField(help_text='Name of the customer associated with this audit', max_length=100),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='destination_hostnames',
            field=models.JSONField(blank=True, default=list, help_text='List of destination hostnames', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='event_type',
            field=models.CharField(help_text='Type of security event being audited', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='missing_values',
            field=models.BooleanField(default=False, help_text='Flag indicating missing required values'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='priority',
            field=models.CharField(help_text='Priority level of the audit (e.g., P1, P2)', max_length=2, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='recommendations',
            field=models.TextField(blank=True, help_text='Recommendations for addressing security issues', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='remediation',
            field=models.BooleanField(default=False, help_text='Whether remediation actions were taken'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='remediation_action',
            field=models.CharField(blank=True, help_text='Description of remediation actions taken', max_length=1000,
                                   null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='remediation_reason',
            field=models.CharField(blank=True, help_text='Reason for remediation actions', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='structural_issues',
            field=models.BooleanField(default=False, help_text='Flag indicating structural issues in the audit'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='ticket',
            field=models.CharField(help_text='Unique alphanumeric ticket identifier', max_length=15, unique=True,
                                   validators=[
                                       django.core.validators.RegexValidator(message='Ticket must be alphanumeric',
                                                                             regex='^[A-Za-z0-9]+$')]),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='ticket_date',
            field=models.DateTimeField(blank=True, help_text='Date and time when the ticket was created', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='Date and time when this record was last updated'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='wsa_link',
            field=models.URLField(blank=True, help_text='Link to Web Security Appliance details', max_length=1000,
                                  null=True),
        ),
        migrations.AddIndex(
            model_name='auditresults',
            index=models.Index(fields=['ticket'], name='auditor_aud_ticket_32c040_idx'),
        ),
        migrations.AddIndex(
            model_name='auditresults',
            index=models.Index(fields=['customer_name'], name='auditor_aud_custome_86a8d6_idx'),
        ),
        migrations.AddIndex(
            model_name='auditresults',
            index=models.Index(fields=['updated_at'], name='auditor_aud_updated_86c2fb_idx'),
        ),
    ]
