# Generated by Django 5.2 on 2025-05-05 15:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auditor', '0006_alter_auditresults_action_history_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='auditresults',
            name='original_filename',
            field=models.CharField(blank=True, help_text='Original filename of the PDF report', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='action_history',
            field=models.JSONField(blank=True, default=list, help_text="Stores a history log of changes made to the 'action_required' status (user, timestamp, comment)", null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='action_required',
            field=models.BooleanField(default=False, help_text='Flag indicating if manual review or action is required due to detected issues'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='blacklist_comments',
            field=models.TextField(blank=True, help_text='Comments regarding the status of blacklisted IPs (e.g., verification results)', null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='auditresults',
            name='blacklisted_ips',
            field=models.J<PERSON>NField(blank=True, default=list, help_text='List of IP addresses reported as blacklisted', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='create_date',
            field=models.DateTimeField(auto_now_add=True, help_text='Date and time when this database record was created'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='Date and time when this database record was created'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='destination_hostnames',
            field=models.JSONField(blank=True, default=list, help_text='List of destination hostnames mentioned in the report', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='event_type',
            field=models.CharField(help_text='Type of security event being audited (e.g., DDoS, WAF)', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='missing_values',
            field=models.BooleanField(default=False, help_text='Flag indicating if the report analysis detected missing required values'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='recommendations',
            field=models.TextField(blank=True, help_text='Recommendations provided in the report for addressing security issues', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='remediation',
            field=models.BooleanField(default=False, help_text='Whether remediation actions were reported as taken'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='remediation_action',
            field=models.CharField(blank=True, help_text='Description of remediation actions taken (from the report)', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='remediation_reason',
            field=models.CharField(blank=True, help_text='Reason provided for the remediation actions (from the report)', max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='structural_issues',
            field=models.BooleanField(default=False, help_text='Flag indicating if structural issues (e.g., template comments, language mix) were found in the report'),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='structure_comments',
            field=models.TextField(blank=True, help_text='Comments regarding structural issues found in the report (e.g., missing fields, language issues)', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='ticket_date',
            field=models.DateTimeField(blank=True, help_text='Date and time when the ticket was created (from the report)', null=True),
        ),
        migrations.AlterField(
            model_name='auditresults',
            name='wsa_link',
            field=models.URLField(blank=True, help_text='Link to Web Security Appliance (WSA) or related control panel details', max_length=1000, null=True),
        ),
    ]
