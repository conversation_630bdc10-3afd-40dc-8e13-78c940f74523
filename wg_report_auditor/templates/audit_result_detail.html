{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container py-4">
    <!-- Header with actions -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="display-6 mb-1">
                <i class="fas fa-file-alt text-primary me-2"></i>Audit Result: {{ result.ticket }}
            </h1>
            <p class="text-muted">{{ result.customer_name }}</p>
        </div>
        <a href="{% url 'auditor:audit-results-list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Results
        </a>
    </div>

    <div class="row g-4">
        <!-- Left column - Basic information -->
        <div class="col-lg-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Ticket ID:</span>
                            <span class="{% if result.ticket|slice:':4' == 'NOID' %} text-danger-persistent fw-bold{% else %}fw-bold{% endif %}">
                                {{ result.ticket }}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Customer Name:</span>
                            <span class="fw-bold">{{ result.customer_name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Ticket Date:</span>
                            <span class="fw-bold">{{ result.ticket_date }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Event Type:</span>
                            <span class="fw-bold">{{ result.event_type }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Priority:</span>
                            <span>
                                {% if result.priority == 'high' or result.priority == 'P1' %}
                                    <span class="badge bg-danger">{{ result.priority }}</span>
                                {% elif result.priority == 'medium' or result.priority == 'P2' %}
                                    <span class="badge bg-warning text-dark">{{ result.priority }}</span>
                                {% elif result.priority == 'low' or result.priority == 'P3' %}
                                    <span class="badge bg-success">{{ result.priority }}</span>
                                {% elif result.priority == 'P4' %}
                                    <span class="badge bg-info">{{ result.priority }}</span>
                                {% elif result.priority == None or result.priority == '' %}
                                    <span class="badge bg-dark text-warning fw-bold">Missing Priority</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ result.priority }}</span>
                                {% endif %}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Created:</span>
                            <span class="fw-bold">{{ result.created_at }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Last Updated:</span>
                            <span class="fw-bold">{{ result.updated_at }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Right column - Status information at the top -->
        <div class="col-lg-8">
            <!-- Status Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Status Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Action Required:</span>
                            <div class="d-flex align-items-center">
                                <span id="actionRequiredBadge" class="badge {% if result.action_required %}bg-warning{% else %}bg-success{% endif %} me-2">
                                    {{ result.action_required|yesno:"Yes,No" }}
                                </span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="toggleActionRequired" 
                                           {% if result.action_required %}checked{% endif %}
                                           data-audit-id="{{ result.id }}">
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Remediation:</span>
                            <span class="badge {% if result.remediation %}bg-success{% else %}bg-secondary{% endif %}">
                                {{ result.remediation|yesno:"Yes,No" }}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Missing Values:</span>
                            <span class="badge {% if result.missing_values %}bg-warning{% else %}bg-success{% endif %}">
                                {{ result.missing_values|yesno:"Yes,No" }}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between px-0">
                            <span class="text-muted">Structural Issues:</span>
                            <span class="badge {% if result.structural_issues %}bg-warning{% else %}bg-success{% endif %}">
                                {{ result.structural_issues|yesno:"Yes,No" }}
                            </span>
                        </li>
                    </ul>
                    
                    <!-- Action History Section -->
                    <div class="mt-4">
                        <h6 class="text-muted mb-2">Action History:</h6>
                        <div id="actionHistoryContainer" class="p-3 bg-light rounded">
                            {% if result.action_history %}
                                <ul class="list-group list-group-flush">
                                {% for entry in result.action_history %}
                                    <li class="list-group-item bg-transparent px-0">
                                        <div class="d-flex justify-content-between">
                                            <span>
                                                <strong>{{ entry.user }}</strong> 
                                                {{ entry.action }} action required
                                            </span>
                                            <small class="text-muted">{{ entry.timestamp }}</small>
                                        </div>
                                        {% if entry.comment %}
                                            <p class="mb-0 text-muted small">Comment: {{ entry.comment }}</p>
                                        {% endif %}
                                    </li>
                                {% endfor %}
                                </ul>
                            {% else %}
                                <p class="text-muted mb-0">No action history available</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Remediation Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-wrench me-2"></i>Remediation Details</h5>
                </div>
                <div class="card-body">
                    {% if result.remediation_action or result.remediation_reason %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Remediation Action:</h6>
                            <div class="p-3 bg-light rounded">
                                <p class="mb-0">{{ result.remediation_action|default:"No action specified" }}</p>
                            </div>
                        </div>
                        <div>
                            <h6 class="text-muted mb-2">Remediation Reason:</h6>
                            <div class="p-3 bg-light rounded">
                                <p class="mb-0">{{ result.remediation_reason|default:"No reason specified" }}</p>
                            </div>
                        </div>
                    {% else %}
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>No remediation information available
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Hostnames and IPs -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-network-wired me-2"></i>Network Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <h6 class="text-muted mb-2">Destination Hostnames:</h6>
                            {% if result.destination_hostnames %}
                                <ul class="list-group">
                                    {% for hostname in result.destination_hostnames %}
                                        <li class="list-group-item">{{ hostname }}</li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <div class="alert alert-secondary">No hostnames available</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Blacklisted IPs:</h6>
                            {% if result.blacklisted_ips %}
                                <ul class="list-group">
                                    {% for ip in result.blacklisted_ips %}
                                        <li class="list-group-item">{{ ip }}</li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <div class="alert alert-secondary">No blacklisted IPs</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if result.blacklist_comments %}
                        <div class="mt-3">
                            <h6 class="text-muted mb-2">Blacklist Comments:</h6>
                            <div class="p-3 bg-light rounded">
                                <p class="mb-0">{{ result.blacklist_comments }}</p>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if result.wsa_link %}
                        <div class="mt-3">
                            <h6 class="text-muted mb-2">WSA Link:</h6>
                            <a href="{{ result.wsa_link }}" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i> Open WSA Link
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Additional Information</h5>
                </div>
                <div class="card-body">
                    {% if result.recommendations %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Recommendations:</h6>
                            <div class="p-3 bg-light rounded">
                                <pre class="mb-0">{{ result.recommendations }}</pre>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if result.structure_comments %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Structure Comments:</h6>
                            <div class="p-3 bg-light rounded">
                                <pre class="mb-0">{{ result.structure_comments }}</pre>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if result.code_snippet %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Code Snippet:</h6>
                            <div class="p-3 bg-dark text-light rounded">
                                <pre><code>{{ result.code_snippet }}</code></pre>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if result.issue_description %}
                        <div>
                            <h6 class="text-muted mb-2">Issue Description:</h6>
                            <div class="p-3 bg-light rounded">
                                <pre class="mb-0">{{ result.issue_description }}</pre>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if result.notes %}
                        <div class="mt-3">
                            <h6 class="text-muted mb-2">Notes:</h6>
                            <div class="p-3 bg-light rounded">
                                <pre class="mb-0">{{ result.notes }}</pre>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- History Information (new section) -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-white py-3">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Change History</h5>
    </div>
    <div class="card-body">
        <div id="historyContainer">
            <div class="text-center py-3">
                <button id="loadHistory" class="btn btn-outline-primary" data-audit-id="{{ result.id }}">
                    <i class="fas fa-clock me-1"></i> Load Change History
                </button>
            </div>
        </div>
    </div>
</div>

{% block modals %}
<!-- Comment Modal -->
<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentModalLabel">Add Comment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="commentForm">
                    <div class="mb-3">
                        <label for="actionComment" class="form-label">Please provide a reason for this change:</label>
                        <textarea class="form-control" id="actionComment" rows="3" required></textarea>
                        <div class="form-text">This comment will be recorded in the action history.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitComment">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Bootstrap version:', bootstrap.Modal.VERSION);
        
        // Debug output to verify modal element exists
        const commentModalElement = document.getElementById('commentModal');
        console.log('Modal element found:', commentModalElement !== null);
        
        const toggleSwitch = document.getElementById('toggleActionRequired');
        const badge = document.getElementById('actionRequiredBadge');
        
        // Initialize the Bootstrap modal
        let commentModal = null;
        if (commentModalElement) {
            try {
                commentModal = new bootstrap.Modal(commentModalElement);
                console.log('Modal initialized successfully');
            } catch (error) {
                console.error('Error initializing modal:', error);
            }
        } else {
            console.error('Modal element not found in the DOM');
        }
        
        // Store the initial state of the toggle
        let initialToggleState = toggleSwitch ? toggleSwitch.checked : false;
        console.log("Initial toggle state:", initialToggleState);
        
        if (toggleSwitch) {
            toggleSwitch.addEventListener('change', function() {
                const auditId = this.getAttribute('data-audit-id');
                const newState = this.checked;
                
                console.log("Toggle changed to:", newState);
                console.log("Previous state was:", initialToggleState);
                
                // Update modal title based on action
                const modalTitle = document.getElementById('commentModalLabel');
                if (modalTitle) {
                    if (newState) {
                        modalTitle.textContent = 'Enable "Action Required"';
                    } else {
                        modalTitle.textContent = 'Disable "Action Required"';
                    }
                }
                
                // Clear previous comment
                const commentField = document.getElementById('actionComment');
                if (commentField) {
                    commentField.value = '';
                }
                
                // Show the modal
                if (commentModal) {
                    commentModal.show();
                    console.log("Modal shown");
                } else {
                    console.error("Modal not initialized");
                    // Revert the toggle
                    this.checked = initialToggleState;
                    alert("Error: Could not initialize comment form. Please refresh the page.");
                }
            });
        }
        
        // Add this code for the submit button in the modal
        const submitButton = document.getElementById('submitComment');
        if (submitButton) {
            submitButton.addEventListener('click', function() {
                const comment = document.getElementById('actionComment').value;
                const toggleSwitch = document.getElementById('toggleActionRequired');
                const newState = toggleSwitch ? toggleSwitch.checked : false;
                const auditId = toggleSwitch ? toggleSwitch.getAttribute('data-audit-id') : '';
                
                console.log("Submit comment clicked, comment:", comment);
                console.log("Sending new state to server:", newState);
                
                // Hide the modal
                if (commentModal) {
                    commentModal.hide();
                }
                
                // Show loading indicator
                const loadingIndicator = document.createElement('span');
                loadingIndicator.className = 'ms-2 spinner-border spinner-border-sm';
                loadingIndicator.id = 'toggleLoadingIndicator';
                if (badge && badge.parentNode) {
                    badge.parentNode.appendChild(loadingIndicator);
                }
                
                // Send AJAX request to update the status
                fetch('{% url "auditor:toggle-action-required" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({
                        audit_id: auditId,
                        action_required: newState,
                        comment: comment || ''
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Response:", data);
                    
                    // Remove loading indicator
                    const indicator = document.getElementById('toggleLoadingIndicator');
                    if (indicator) indicator.remove();
                    
                    if (data.success) {
                        // Update the initial state to match the new server state
                        initialToggleState = data.action_required;
                        
                        // Make sure the toggle switch reflects the server state
                        if (toggleSwitch) {
                            toggleSwitch.checked = data.action_required;
                            console.log("Toggle updated to match server state:", data.action_required);
                        }
                        
                        // Update the badge
                        if (badge) {
                            if (data.action_required) {
                                badge.classList.remove('bg-success');
                                badge.classList.add('bg-warning');
                                badge.textContent = 'Yes';
                            } else {
                                badge.classList.remove('bg-warning');
                                badge.classList.add('bg-success');
                                badge.textContent = 'No';
                            }
                            console.log("Badge updated to:", badge.textContent);
                        } else {
                            console.error("Badge element not found");
                        }
                        
                        // Refresh the action history section if it exists
                        refreshActionHistory(auditId);
                    } else {
                        alert('Error: ' + (data.error || 'Unknown error'));
                        // Revert the toggle if there was an error
                        if (toggleSwitch) {
                            toggleSwitch.checked = initialToggleState;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the status: ' + error.message);
                    
                    // Remove loading indicator
                    const indicator = document.getElementById('toggleLoadingIndicator');
                    if (indicator) indicator.remove();
                    
                    // Revert the toggle if there was an error
                    if (toggleSwitch) {
                        toggleSwitch.checked = initialToggleState;
                    }
                });
            });
        }
        
        // Add a cancel button handler to revert the toggle
        const cancelButton = document.querySelector('#commentModal .btn-secondary');
        if (cancelButton) {
            cancelButton.addEventListener('click', function() {
                console.log("Cancel button clicked");
                // Revert the toggle since the user canceled
                const toggleSwitch = document.getElementById('toggleActionRequired');
                if (toggleSwitch) {
                    toggleSwitch.checked = initialToggleState;
                    console.log("Toggle reverted to initial state:", initialToggleState);
                }
            });
        }
        
        // Also handle the modal close button (X) and ESC key
        if (commentModalElement) {
            commentModalElement.addEventListener('hidden.bs.modal', function() {
                console.log("Modal hidden event triggered");
                // Revert the toggle if the modal was dismissed without submitting
                const toggleSwitch = document.getElementById('toggleActionRequired');
                if (toggleSwitch) {
                    toggleSwitch.checked = initialToggleState;
                    console.log("Toggle reverted to initial state:", initialToggleState);
                }
            });
        }
        
        // Function to refresh the action history
        function refreshActionHistory(auditId) {
            const historyContainer = document.getElementById('actionHistoryContainer');
            if (!historyContainer) {
                console.error("History container not found");
                return;
            }
            
            // Show loading indicator in history container
            historyContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            
            // Use the correct URL pattern with the audit_id in the path
            fetch(`{% url "auditor:get-audit-history" audit_id=0 %}`.replace('0', auditId))
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.action_history) {
                        if (data.action_history.length > 0) {
                            let html = '<ul class="list-group list-group-flush">';
                            data.action_history.forEach(entry => {
                                html += `
                                    <li class="list-group-item bg-transparent px-0">
                                        <div class="d-flex justify-content-between">
                                            <span>
                                                <strong>${entry.user}</strong> 
                                                ${entry.action} action required
                                            </span>
                                            <small class="text-muted">${entry.timestamp}</small>
                                        </div>
                                        ${entry.comment ? `<p class="mb-0 text-muted small">Comment: ${entry.comment}</p>` : ''}
                                    </li>
                                `;
                            });
                            html += '</ul>';
                            historyContainer.innerHTML = html;
                        } else {
                            historyContainer.innerHTML = '<p class="text-muted mb-0">No action history available</p>';
                        }
                    } else {
                        historyContainer.innerHTML = '<div class="alert alert-warning">Failed to load history</div>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching history:', error);
                    historyContainer.innerHTML = '<div class="alert alert-danger">Error loading history</div>';
                });
        }
    });
</script>
{% endblock %}
