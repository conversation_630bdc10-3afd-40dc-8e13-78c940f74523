<div id="client-list-container">
    <label for="id_client_list">Client List:</label>
    <select name="client_list" id="id_client_list" hx-get="{% url 'populate_clientlist_items' %}" hx-trigger="change" hx-target="#selection-message" hx-swap="outerHTML">
        <option value="">Select a client list</option>
        {% for client_list in client_lists %}
            <option value="{{ client_list.pk }}">{{ client_list.name }}</option>
        {% endfor %}
    </select>
</div>
