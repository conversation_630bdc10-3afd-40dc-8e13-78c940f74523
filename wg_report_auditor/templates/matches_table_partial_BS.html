{% load custom_filters %}
<div id="output" class="container mt-5" >
    <h1 class="text-center mb-4">Lookup Results</h1>
    <table class="table table-bordered align-middle">
        <thead>
            <tr>
                <th class="table-dark">User Input</th>
                <th class="table-dark"></th> <!-- Spacer column -->
                {% for column in columns %}
                    {% if column != "UserInput" %}
                    <th class="table-dark">{{ column }}</th>
                    {% endif %}
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for row in rows %}
            <tr>
                <td class="table-primary" style="width: 30%;">{{ row.UserInput }}</td> <!-- 'User Input' column -->
                <td style="width: 10%;"></td> <!-- Spacer column -->
                {% for column in columns %}
                    {% if column != "UserInput" %}
                    <td class="table-danger">{{ row|get_item:column }}</td> <!-- Dynamically display other column values -->
                    {% endif %}
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>