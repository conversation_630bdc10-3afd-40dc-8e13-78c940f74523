{% load static %}
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <div class="container">
    <a class="navbar-brand" href="{% url 'auditor:upload_file' %}"><img src="{% static 'img/OCD_Logo.png' %}" alt="Orange Cyberdefense" height="30" class="d-inline-block align-top me-2">
                WebGuardian Report Audit Assistant
            </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" href="{% url 'auditor:upload_file' %}">Start Audit</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{% url 'auditor:audit-results-list' %}">Results</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{% url 'auditor:admin_actions' %}">Database Actions</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="{% url 'auditor:dashboard' %}">Dashboard</a>
        </li>
      </ul>
      
      <!-- User information on the right side -->
      <ul class="navbar-nav ms-auto">
        {% if user.is_authenticated %}
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              {{ user.get_full_name|default:user.username }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
              <li><span class="dropdown-item-text">Email: {{ user.email }}</span></li>
              {% if user.is_staff %}
              <li><a class="dropdown-item" href="/admin/">Admin Panel</a></li>
              {% endif %}
              <li><hr class="dropdown-divider"></li>
              <li>
                <form id="logout-form" method="post" action="{% url 'auditor:logout' %}" class="dropdown-item p-0">
                  {% csrf_token %}
                  <button type="submit" class="btn btn-link text-decoration-none text-danger w-100 text-start px-3 py-1">
                    Logout
                  </button>
                </form>
              </li>
            </ul>
          </li>
        {% else %}
          <li class="nav-item">
            <a class="nav-link" href="{% url 'auditor:login' %}">Login</a>
          </li>
        {% endif %}
      </ul>
    </div>
  </div>
</nav>

<style>
  #logout-form button {
    background: none;
    border: none;
  }
</style>
