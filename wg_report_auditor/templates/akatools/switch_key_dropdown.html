<div id="switch-key-container">
    <label for="switch_key" class="font-weight-bold">Switch Key:</label>
    <select name="switch_key" id="switch_key" class="form-control"
            hx-get="{% url 'akatools:load_cpcodes' %}"
            hx-trigger="change"
            hx-target="#cpcode-container"
            hx-swap="outerHTML"
            hx-include="[name='client'], [name='switch_key']">
        <option value="">Select a switch key</option>
        {% for switch_key in switch_keys %}
            <option value="{{ switch_key.key }}" {% if selected_switch_key == switch_key.key %}selected{% endif %}>
                {{ switch_key.key }} ({{ switch_key.name }})
            </option>
        {% endfor %}
    </select>
    <!-- Debug info -->
    <small class="text-muted">Debug: Found {{ switch_keys|length }} switch keys</small>
</div>