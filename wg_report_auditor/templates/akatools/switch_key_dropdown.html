<div id="switch-key-container">
    <label for="switch_key" class="font-weight-bold">Switch Key:</label>
    <select name="switch_key" id="switch_key" class="form-control"
            hx-get="{% url 'akatools:load_cpcodes' %}" 
            hx-trigger="change" 
            hx-target="#cpcode-container" 
            hx-swap="outerHTML"
            hx-include="[name='client']">
        <option value="{{ client.key }}" {% if selected_switch_key == client.key %}selected{% endif %}>{{ client.key }}</option>
    </select>
</div>