{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar text-primary"></i>
            Akamai Traffic Report Generator
        </h1>
        <div class="d-flex">
            {% if charts %}
            <a href="{% url 'akatools:download_traffic_report' %}?duration={{ form_data.duration }}&cpcode={{ form_data.cpcode }}&limit={{ form_data.limit }}&client={{ form_data.client }}&switch_key={{ form_data.switch_key }}" 
               class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm mr-2">
                <i class="fas fa-download fa-sm text-white-50"></i> Download Report
            </a>
            {% endif %}
            <a href="{% url 'auditor:dashboard' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-tachometer-alt fa-sm text-white-50"></i> Dashboard
            </a>
        </div>
    </div>

    <!-- Configuration Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog"></i> Report Configuration
                    </h6>
                </div>
                <div class="card-body">
                    <form id="trafficReportForm" method="post" class="mb-4">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="duration" class="font-weight-bold">Time Range:</label>
                                    <select name="duration" id="duration" class="form-control">
                                        {% for value, label in duration_choices %}
                                            <option value="{{ value }}" {% if form_data.duration == value %}selected{% endif %}>
                                                {{ label }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="client" class="font-weight-bold">Client:</label>
                                    <select name="client" id="client" class="form-control" 
                                            hx-get="{% url 'akatools:load_switch_keys' %}" 
                                            hx-trigger="change" 
                                            hx-target="#switch-key-container" 
                                            hx-swap="outerHTML"
                                            hx-include="[name='switch_key']">
                                        <option value="">Select a client</option>
                                        {% for client in clients %}
                                            <option value="{{ client.key }}" {% if form_data.client == client.key %}selected{% endif %}>{{ client.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div id="switch-key-container">
                                    <label for="switch_key" class="font-weight-bold">Switch Key:</label>
                                    <select name="switch_key" id="switch_key" class="form-control" 
                                            {% if not form_data.client %}disabled{% endif %}
                                            hx-get="{% url 'akatools:load_cpcodes' %}" 
                                            hx-trigger="change" 
                                            hx-target="#cpcode-container" 
                                            hx-swap="outerHTML"
                                            hx-include="[name='client']">
                                        <option value="">{% if form_data.client %}{{ form_data.switch_key }}{% else %}Select a client first{% endif %}</option>
                                        {% if form_data.client and form_data.switch_key %}
                                            <option value="{{ form_data.switch_key }}" selected>{{ form_data.switch_key }}</option>
                                        {% endif %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div id="cpcode-container">
                                    <label for="cpcode" class="font-weight-bold">CP Code:</label>
                                    <select name="cpcode" id="cpcode" class="form-control" {% if not form_data.switch_key %}disabled{% endif %}>
                                        <option value="{{ form_data.cpcode }}">{% if form_data.cpcode %}{{ form_data.cpcode }}{% else %}Select a switch key first{% endif %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="limit" class="font-weight-bold">Limit:</label>
                                    <input type="number" name="limit" id="limit" class="form-control" 
                                           value="{{ form_data.limit }}" min="1" max="100">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block" id="generateBtn">
                                        <i class="fas fa-play"></i> Generate
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div id="loadingIndicator" style="display: none;" class="mt-3">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border text-primary mr-2" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <div>Generating report, please wait... This may take a few moments.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Generating traffic report... Please wait.</p>
                </div>
            </div>
        </div>
    </div>

    {% if stats %}
    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Hostnames</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_hostnames }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Bytes (GB)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_edge_bytes_gb }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Hits (M)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_edge_hits_millions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 col-sm-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Top by Bytes</div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">{{ stats.top_hostname_by_bytes|truncatechars:20 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 col-sm-6 mb-3">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Top by Hits</div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">{{ stats.top_hostname_by_hits|truncatechars:20 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if charts %}
    <!-- Charts Section -->
    <div class="row">
        {% if charts.bytes_chart %}
        <div class="col-xl-6 col-lg-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i> Traffic by Bytes
                    </h6>
                </div>
                <div class="card-body">
                    {{ charts.bytes_chart|safe }}
                </div>
            </div>
        </div>
        {% endif %}

        {% if charts.hits_chart %}
        <div class="col-xl-6 col-lg-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line"></i> Traffic by Hits
                    </h6>
                </div>
                <div class="card-body">
                    {{ charts.hits_chart|safe }}
                </div>
            </div>
        </div>
        {% endif %}

        {% if charts.combined_chart %}
        <div class="col-xl-12 col-lg-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-area"></i> Combined Traffic Overview
                    </h6>
                </div>
                <div class="card-body">
                    {{ charts.combined_chart|safe }}
                </div>
            </div>
        </div>
        {% endif %}

        {% if charts.pie_chart %}
        <div class="col-xl-12 col-lg-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i> Traffic Distribution
                    </h6>
                </div>
                <div class="card-body">
                    {{ charts.pie_chart|safe }}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    {% if not charts and not stats %}
    <!-- Welcome Message -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-chart-bar fa-5x text-gray-300 mb-4"></i>
                    <h4 class="text-gray-600">Welcome to Traffic Report Generator</h4>
                    <p class="text-gray-500 mb-4">
                        Configure your report parameters above and click "Generate" to create beautiful interactive charts 
                        showing your Akamai traffic data.
                    </p>
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="alert alert-info">
                                <strong>Features:</strong>
                                <ul class="list-unstyled mb-0 mt-2">
                                    <li><i class="fas fa-check text-success"></i> Interactive Plotly charts</li>
                                    <li><i class="fas fa-check text-success"></i> Multiple visualization types</li>
                                    <li><i class="fas fa-check text-success"></i> Downloadable HTML reports</li>
                                    <li><i class="fas fa-check text-success"></i> Real-time Akamai API data</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('trafficReportForm');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const generateBtn = document.getElementById('generateBtn');
    
    form.addEventListener('submit', function(e) {
        // Show loading indicator
        loadingIndicator.style.display = 'block';
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
        
        // Scroll to loading indicator
        loadingIndicator.scrollIntoView({ behavior: 'smooth' });
    });
    
    // Auto-resize charts when window is resized
    window.addEventListener('resize', function() {
        window.dispatchEvent(new Event('resize'));
    });
    
    // Debug HTMX events
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        console.log('HTMX request starting:', event.detail);
    });
    
    document.body.addEventListener('htmx:afterRequest', function(event) {
        console.log('HTMX request completed:', event.detail);
    });
    
    document.body.addEventListener('htmx:responseError', function(event) {
        console.error('HTMX response error:', event.detail);
    });
});
</script>
{% endblock %}
