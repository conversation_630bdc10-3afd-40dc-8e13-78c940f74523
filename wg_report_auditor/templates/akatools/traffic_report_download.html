<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Akamai Traffic Report - {{ form_data.cpcode }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            margin: 5px 0;
        }
        .stats-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .stat-box {
            flex: 1;
            min-width: 200px;
            margin: 10px;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-box h3 {
            margin-top: 0;
            color: #3498db;
            font-size: 16px;
        }
        .stat-box p {
            font-size: 24px;
            font-weight: bold;
            margin: 5px 0;
        }
        .chart-container {
            margin-bottom: 40px;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Akamai Traffic Report</h1>
            <p>CP Code: {{ form_data.cpcode }}</p>
            <p>Time Range: {{ form_data.duration }}</p>
            <p>Generated: {{ timestamp }}</p>
        </div>
        
        <div class="stats-container">
            <div class="stat-box">
                <h3>Total Hostnames</h3>
                <p>{{ stats.total_hostnames }}</p>
            </div>
            <div class="stat-box">
                <h3>Total Traffic</h3>
                <p>{{ stats.total_edge_bytes_gb }} GB</p>
            </div>
            <div class="stat-box">
                <h3>Total Hits</h3>
                <p>{{ stats.total_edge_hits_millions }} M</p>
            </div>
            <div class="stat-box">
                <h3>Top Hostname</h3>
                <p>{{ stats.top_hostname_by_bytes }}</p>
            </div>
        </div>
        
        {% if charts.bytes_chart %}
        <div class="chart-container">
            {{ charts.bytes_chart|safe }}
        </div>
        {% endif %}
        
        {% if charts.hits_chart %}
        <div class="chart-container">
            {{ charts.hits_chart|safe }}
        </div>
        {% endif %}
        
        {% if charts.combined_chart %}
        <div class="chart-container">
            {{ charts.combined_chart|safe }}
        </div>
        {% endif %}
        
        {% if charts.pie_chart %}
        <div class="chart-container">
            {{ charts.pie_chart|safe }}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>This report was generated automatically by the Akamai Traffic Report Generator.</p>
            <p>Data Count: {{ data_count }} hostnames</p>
        </div>
    </div>
</body>
</html>