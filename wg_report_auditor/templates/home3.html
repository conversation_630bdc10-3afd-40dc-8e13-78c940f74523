<!DOCTYPE html>
<html>
<head>
    <title>Cascading Dropdowns with Welcome Message</title>
    <script src="https://unpkg.com/htmx.org@1.6.1"></script>
    <script>
    htmx.on('htmx:beforeRequest', (evt) => {
        console.log("Making request to: ", evt.detail.verb, evt.detail.path);
    });
    htmx.on('htmx:beforeSwap', (evt) => {
        console.log("Received response: ", evt.detail.xhr.responseText);
    });
    </script>
</head>
<body>
    <h1>Welcome to the Akamai tools application {{ apiUser.name }}</h1>
    <br><br>
    {% if not apiUser.accountSwitching %}
    Unfortunately, it seems that you have limited access.
    <br><br>
    Please contact your Akamai Administrator to create an API client that can manage other accounts as well.
    {% endif %}
    <h1>Select a Client and a ClientList</h1>
    <form method="get" action="{% url 'items_form_page' %}">
        {% csrf_token %}
        <label for="id_client">Client:</label>
        <select name="client" id="id_client" hx-get="{% url 'client_lists' %}" hx-trigger="change" hx-target="#client-list-container" hx-swap="outerHTML">
            <option value="">Select a client</option>
            {% for client in form.fields.client.queryset %}
                <option value="{{ client.key }}">{{ client.name }}</option>
            {% endfor %}
        </select>
        <br><br>
        <div id="client-list-container">
        <label for="id_client_list">Client List:</label>
        <select name="client_list" id="id_client_list" hx-get="{% url 'aktools:populate_clientlist_items' %}" hx-trigger="change" hx-target="#result" hx-swap="outerHTML">
            <option value="">Select a client first</option>
            {% for clientlist in form.fields.clientlists.queryset %}
                <option value="{{ clientlist.pk }}">{{ clientlist.name }}</option>
            {% endfor %}
        </select>
        </div>
        <br>
        <div id="result"></div>
        <br>
    <div id="result"></div>
        <br>
        <input type="submit" value="Next">
    </form>
    <br>
    <button hx-get="{% url 'refresh_data' %}" hx-trigger="click" hx-target="body" hx-swap="outerHTML">Refresh</button>
</body>
</html>