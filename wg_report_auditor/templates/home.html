<!DOCTYPE html>
<html>
<head>
    <title>Cascading Dropdowns</title>
    <script src="https://unpkg.com/htmx.org@1.4.1"></script>
</head>
<body>
    <h1>Select a Client and a ClientList</h1>
    <form method="post">
        {% csrf_token %}
        <label for="id_client">Client:</label>
        <select name="client" id="id_client" hx-get="{% url 'client_lists' %}" hx-trigger="change" hx-target="#client-list-container" hx-swap="outerHTML">
            <option value="">Select a client</option>
            {% for client in form.fields.client.queryset %}
                <option value="{{ client.pk }}">{{ client.name }}</option>
            {% endfor %}
        </select>
        <br><br>
        <div id="client-list-container" hx-swap-oob="true">
            <label for="id_client_list">Client List:</label>
            <select name="client_list" id="id_client_list" disabled>
                <option value="">Select a client first</option>
            </select>
        </div>
        <br><br>
        <div id="selection-message" hx-swap-oob="true"></div>
        <input type="submit" value="Submit">
    </form>
    <br>
    <button hx-get="{% url 'refresh_data' %}" hx-trigger="click" hx-target="body" hx-swap="outerHTML">Refresh</button>
</body>
</html>