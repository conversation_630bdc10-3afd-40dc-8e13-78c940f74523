<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Styled DataFrame</title>
    <style>
        .container {
            display: flex;
            align-items: flex-start;
            gap: 50px; /* Add spacing between the two tables */
        }
        .user-input-table, .main-table {
            border-collapse: collapse;
            width: auto;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: yellow; /* Highlighted cells */
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- User Input Table -->
        <table class="user-input-table">
            <thead>
                <tr>
                    <th>User Input Matches</th>
                </tr>
            </thead>
            <tbody>
                {% for row in user_input_rows %}
                <tr>
                    <td class="{{ row.highlight_class }}">{{ row.user_input }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Main DataFrame Table -->
        <table class="main-table">
            <thead>
                <tr>
                    {% for col_name in main_columns %}
                    <th>{{ col_name }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for row in main_rows %}
                <tr>
                    {% for cell in row %}
                    <td class="{{ cell.highlight_class }}">{{ cell.value }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>
