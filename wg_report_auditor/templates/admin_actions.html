{% extends "base.html" %}
{% load static %}

{% block title %}Database Actions{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-dark text-white">
                    <h2 class="mb-0"><i class="fas fa-tools me-2"></i>Database Actions</h2>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}warning{% endif %} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> The actions on this page can have significant impacts on the application data.
                        Please proceed with caution.
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-trash-alt me-2"></i>Clear Database</h5>
                        </div>
                        <div class="card-body">
                            <p>This action will delete all data from the database except user accounts. This cannot be undone.</p>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#clearDatabaseModal">
                                <i class="fas fa-trash-alt me-2"></i>Clear Database
                            </button>
                        </div>
                    </div>
                    
                    <!-- Add more admin actions here in the future -->
                    
                </div>
                <div class="card-footer">
                    <a href="{% url 'auditor:test_auth_page' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clear Database Confirmation Modal -->
<div class="modal fade" id="clearDatabaseModal" tabindex="-1" aria-labelledby="clearDatabaseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="clearDatabaseModalLabel">Confirm Database Clear</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Warning:</strong> This action will delete ALL data from the database except user accounts.
                </div>
                <p>This action cannot be undone. Are you absolutely sure you want to proceed?</p>
                <p>Type "CLEAR DATABASE" in the field below to confirm:</p>
                <input type="text" id="confirmationText" class="form-control" placeholder="Type CLEAR DATABASE here">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{% url 'auditor:clear_database' %}" method="post" id="clearDatabaseForm">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger" id="confirmClearButton" disabled>
                        <i class="fas fa-trash-alt me-2"></i>Permanently Clear Database
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const confirmationInput = document.getElementById('confirmationText');
        const confirmButton = document.getElementById('confirmClearButton');
        
        confirmationInput.addEventListener('input', function() {
            if (this.value === 'CLEAR DATABASE') {
                confirmButton.disabled = false;
            } else {
                confirmButton.disabled = true;
            }
        });
    });
</script>
{% endblock %}
{% endblock %}