{% extends "base.html" %}
{% load static %}
{% load custom_filters %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h2 class="display-6 mb-0"><i class="fas fa-clipboard-list me-2"></i>Audit Results</h2>
            <p class="text-muted">Browse and filter your audit findings</p>
        </div>
    </div>
    
    <!-- Filter form -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Results</h5>
        </div>
        <div class="card-body p-4">
            <form method="get" class="form">
                <div class="row g-3">
                    {% for field in filter.form %}
                        <div class="col-md-4 col-sm-6">
                            <div class="form-group">
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                {{ field|add_class:"form-control form-control-sm" }}
                                {% if field.help_text %}
                                    <small class="form-text text-muted">{{ field.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Filter
                    </button>
                    <a href="{% url 'auditor:audit-results-list' %}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-undo me-1"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Results table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Results</h5>
            <span class="badge bg-primary rounded-pill fs-6">{{ total_count }} total results</span>
        </div>
        <div class="card-body p-0">
            {% if audit_results %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                {% for field in selected_fields %}
                                    <th class="py-3">{{ field|title }}</th>
                                {% endfor %}
                                <th class="py-3 text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in audit_results %}
                                <tr>
                                    {% for field in selected_fields %}
                                        <td class="align-middle">
                                            {% if field == 'priority' %}
                                                {% if result|getattribute:field == 'high' or result|getattribute:field == 'P1' %}
                                                    <span class="badge bg-danger">{{ result|getattribute:field }}</span>
                                                {% elif result|getattribute:field == 'medium' or result|getattribute:field == 'P2' %}
                                                    <span class="badge bg-warning text-dark">{{ result|getattribute:field }}</span>
                                                {% elif result|getattribute:field == 'low' or result|getattribute:field == 'P3' %}
                                                    <span class="badge bg-success">{{ result|getattribute:field }}</span>
                                                {% elif result|getattribute:field == 'P4' %}
                                                    <span class="badge bg-info">{{ result|getattribute:field }}</span>
                                                {% elif result|getattribute:field == None or result|getattribute:field == '' %}
                                                    <span class="badge bg-dark text-warning fw-bold">Missing Priority</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ result|getattribute:field }}</span>
                                                {% endif %}
                                            {% elif field == 'remediation' %}
                                                <span class="badge {% if result|getattribute:field %}bg-success{% else %}bg-secondary{% endif %}">
                                                    {{ result|getattribute:field|yesno:"Yes,No" }}
                                                </span>
                                            {% elif field == 'action_required' %}
                                                <span class="badge {% if result|getattribute:field %}bg-warning{% else %}bg-success{% endif %}">
                                                    {{ result|getattribute:field|yesno:"Yes,No" }}
                                                </span>
                                            {% else %}
                                                {{ result|getattribute:field }}
                                            {% endif %}
                                        </td>
                                    {% endfor %}
                                    <td class="text-end">
                                        <a href="{% url 'auditor:audit-result-detail' result.id %}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="p-3 border-top">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if query_string %}&{{ query_string }}{% endif %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query_string %}&{{ query_string }}{% endif %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                                
                                {% for i in paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ i }}</span>
                                        </li>
                                    {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ i }}{% if query_string %}&{{ query_string }}{% endif %}">{{ i }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query_string %}&{{ query_string }}{% endif %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ paginator.num_pages }}{% if query_string %}&{{ query_string }}{% endif %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="p-5 text-center">
                    <div class="mb-3">
                        <i class="fas fa-search fa-3x text-muted"></i>
                    </div>
                    <h5>No audit results found</h5>
                    <p class="text-muted">Try adjusting your filters or create a new audit</p>
                    <a href="{% url 'auditor:upload_file' %}" class="btn btn-primary mt-2">
                        <i class="fas fa-plus-circle me-1"></i> Create New Audit
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
{{ block.super }}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
{% endblock %}

{% block extra_js %}
<script>
  $(document).ready(function() {
    // Add click handler for quick toggle buttons
    $('.quick-toggle-action').click(function(e) {
      e.preventDefault();
      
      const btn = $(this);
      const auditId = btn.data('audit-id');
      const comment = prompt("Optional comment for this change:");
      
      // Send AJAX request to toggle status
      $.ajax({
        url: `/auditor/audit/${auditId}/toggle-action/ajax/`,
        method: 'POST',
        data: {
          comment: comment || '',
          csrfmiddlewaretoken: '{{ csrf_token }}'
        },
        success: function(response) {
          if (response.success) {
            // Update button appearance
            if (response.action_required) {
              btn.removeClass('btn-outline-warning').addClass('btn-warning');
              btn.text('Resolve');
              btn.closest('td').find('.status-text').text('Yes');
            } else {
              btn.removeClass('btn-warning').addClass('btn-outline-warning');
              btn.text('Flag');
              btn.closest('td').find('.status-text').text('No');
            }
            
            // Show success message
            alert(response.message);
          }
        },
        error: function() {
          alert('Error updating action required status');
        }
      });
    });
  });
</script>
{% endblock %}
