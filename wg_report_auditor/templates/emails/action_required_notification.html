<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { 
            font-family: 'Segoe UI', Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            padding: 25px; 
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
        }
        .header { 
            background-color: #f8d7da; 
            color: #721c24; 
            padding: 15px 20px; 
            border-radius: 6px;
            border-left: 4px solid #e63946;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 20px;
        }
        .details { 
            margin: 20px 0; 
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }
        .details table { 
            width: 100%; 
            border-collapse: collapse; 
        }
        .details td { 
            padding: 12px 15px; 
            border-bottom: 1px solid #e9ecef; 
        }
        .details td:first-child { 
            font-weight: 600; 
            width: 30%; 
            background-color: #f8f9fa;
            color: #495057;
        }
        .details tr:last-child td {
            border-bottom: none;
        }
        .issues { 
            margin: 20px 0;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px 20px;
        }
        .issues h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 8px;
            font-size: 18px;
        }
        .issues ul {
            padding-left: 20px;
            margin-bottom: 0;
        }
        .issues li {
            margin-bottom: 6px;
        }
        .issues li:last-child {
            margin-bottom: 0;
        }
        .comments { 
            background-color: #f8f9fa; 
            padding: 15px 20px; 
            border-radius: 6px; 
            margin: 20px 0; 
            white-space: pre-line;
            border-left: 3px solid #6c757d;
        }
        .comments h3 {
            margin-top: 0;
            color: #495057;
            font-size: 18px;
        }
        .comments p {
            margin-bottom: 0;
        }
        .recommendations { 
            background-color: #f8f9fa; 
            padding: 15px 20px; 
            border-radius: 6px; 
            margin: 20px 0;
            border-left: 3px solid #28a745;
        }
        .recommendations h3 {
            margin-top: 0;
            color: #495057;
            font-size: 18px;
        }
        .footer { 
            font-size: 12px; 
            color: #6c757d; 
            margin-top: 30px; 
            border-top: 1px solid #e9ecef; 
            padding-top: 15px;
            text-align: center;
        }
        strong {
            color: #dc3545;
            font-weight: 600;
        }
        p strong {
            color: #495057;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Some issues were identified in the following report: {{ audit.ticket }}</h2>
        </div>
        
        <div class="details">
            <table>
                <tr>
                    <td>Ticket:</td>
                    <td>{{ audit.ticket }}</td>
                </tr>
                <tr>
                    <td>Customer:</td>
                    <td>{{ audit.customer_name }}</td>
                </tr>
                <tr>
                    <td>Priority:</td>
                    <td>{{ audit.priority|default:"Not specified" }}</td>
                </tr>
                <tr>
                    <td>Event Type:</td>
                    <td>{{ audit.event_type|default:"Not specified" }}</td>
                </tr>
            </table>
        </div>
        
        <div class="issues">
            <h3>Issues:</h3>
            <ul>
                <li>Missing Values: {% if audit.missing_values %}<strong>Yes</strong>{% else %}No{% endif %}</li>
                <li>Structural Issues: {% if audit.structural_issues %}<strong>Yes</strong>{% else %}No{% endif %}</li>
            </ul>
        </div>

        {% if audit.structure_comments %}
        <div class="comments">
            <h3>Structure Comments:</h3>
            <p style="white-space: pre-line;">{{ audit.structure_comments }}</p>
        </div>
        {% endif %}

        {% if audit.blacklisted_ips %}
            <h3>Blacklisted IPs:</h3>
                <ul>
                    {% for ip in audit.blacklisted_ips %}
                        <li>{{ ip }}</li>
                    {% endfor %}
                </ul>
        {% endif %}
        {% if audit.blacklist_comments %}
        <div class="comments">
            <h3>Blacklist Comments:</h3>
            <p>{{ audit.blacklist_comments|linebreaks }}</p>
        </div>
        {% endif %}
        <!--
        {% if audit.recommendations %}
        <div class="recommendations">
            <h3>Recommendations:</h3>
            <p>{{ audit.recommendations|linebreaks }}</p>
        </div>
        {% endif %}
        -->
        
        <p><strong>Please review the report for {{ audit.ticket }} at your earliest convenience.</strong></p>
        
        <div class="footer">
            This is an automated notification from the WebGuardian Report Audit System.
        </div>
    </div>
</body>
</html>