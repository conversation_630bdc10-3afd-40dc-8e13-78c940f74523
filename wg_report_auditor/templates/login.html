{% extends "base.html" %}
{% load static %}

{% block title %}Login - WebGuardian Report Audit Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="text-center font-weight-light my-2">Login</h3>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}warning{% endif %} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        If you have already stored your API credentials, you can log in here.
                    </div>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login with API Credentials
                            </button>
                        </div>
                    </form>
                    <div class="mt-4 text-center">
                        <hr>
                        <p class="text-muted"> Looking for the Admin Page? <a href="/admin/login/">Login here</a></p>
                    </div>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <i class="fas fa-key me-1"></i>
                        Note: This login uses your Akamai API credentials for authentication. 
                        {% if show_instructions or request.GET.show_instructions %}
                        <a href="{% url 'auditor:auth_setup_instructions' %}" class="text-decoration-none">
                            Need help setting up your API credentials?
                        </a>
                        {% else %}
                        If you do not have them, please refer to the 
                        <a href="{% url 'auditor:auth_setup_instructions' %}" class="text-decoration-none">
                            API setup instructions
                        </a> 
                        to get started.
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}