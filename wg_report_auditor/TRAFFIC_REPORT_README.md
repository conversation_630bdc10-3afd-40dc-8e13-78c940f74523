# Enhanced Akamai Traffic Report Generator

## Overview

The enhanced `trafficreport.py` module provides comprehensive functionality for authenticating with the Akamai API, fetching traffic data, and generating beautiful interactive charts using Plotly. This module has been completely rewritten to include proper authentication, error handling, and multiple visualization options.

## Features

### 🔐 **Secure Authentication**
- Automatic Akamai EdgeGrid authentication
- Proper session management
- Error handling for authentication failures
- Support for multiple account switch keys

### 📊 **Beautiful Visualizations**
- **Bar Charts**: Top hostnames by bytes and hits
- **Combined Charts**: Dual-axis visualization (bytes vs hits)
- **Pie Charts**: Traffic distribution across hostnames
- **Interactive Elements**: Zoom, pan, hover tooltips
- **Responsive Design**: Works on desktop and mobile

### 🌐 **Web Interface**
- Django-integrated web interface
- Real-time report generation
- Downloadable HTML reports
- Summary statistics dashboard
- Form-based configuration

### 🛠️ **Programmatic API**
- Class-based design for easy integration
- Comprehensive error handling
- Flexible parameter configuration
- JSON API endpoints

## Installation & Setup

### Prerequisites
Ensure you have the following dependencies installed (already included in requirements.txt):
```
plotly
pandas
requests
akamai-edgegrid
```

### Akamai API Configuration
1. Ensure your `.edgerc2` file is properly configured in your home directory
2. The file should contain your Akamai API credentials:
```ini
[default]
client_secret = your_client_secret
host = your_host.luna.akamaiapis.net
access_token = your_access_token
client_token = your_client_token
```

## Usage

### 1. Web Interface

#### Access the Traffic Report Generator:
```
http://localhost:8000/akatools/traffic-report/
```

#### Features:
- **Configuration Form**: Set time range, CP code, limit, and switch key
- **Real-time Generation**: Generate reports on-demand
- **Interactive Charts**: Multiple chart types with Plotly
- **Download Reports**: Export as standalone HTML files
- **Summary Statistics**: Key metrics at a glance

### 2. Programmatic Usage

#### Basic Usage:
```python
from akatools.trafficreport import generate_traffic_report

# Generate a complete report
charts, data = generate_traffic_report(
    duration="LAST_1_WEEK",
    cpcode="1657476",
    limit=20,
    save_to_file=True,
    filename="my_traffic_report.html"
)

if charts and data is not None:
    print(f"Generated {len(charts)} charts with {len(data)} records")
```

#### Advanced Usage:
```python
from akatools.trafficreport import AkamaiTrafficReporter

# Create reporter instance
reporter = AkamaiTrafficReporter(switch_key="your_switch_key")

# Authenticate
if reporter.authenticate():
    # Fetch raw data
    raw_data = reporter.get_traffic_data(
        duration="LAST_1_MONTH",
        dimensions=["hostname", "cpcode"],
        metrics=["edgeHitsSum", "edgeBytesSum"],
        limit=50
    )
    
    # Process data
    df = reporter.process_traffic_data(raw_data)
    
    # Generate charts
    charts = reporter.create_traffic_charts(df, "Custom Report")
    
    # Get statistics
    stats = reporter.generate_summary_stats(df)
    
    # Save to file
    reporter.save_charts_to_html(charts, "custom_report.html")

# Always close session
reporter.close_session()
```

### 3. API Endpoints

#### Generate Report (POST):
```
POST /akatools/traffic-report/api/
Content-Type: application/json

{
    "duration": "LAST_1_WEEK",
    "cpcode": "1657476",
    "limit": 20,
    "switch_key": "F-AC-4891513:1-5G3LB"
}
```

#### Download Report (GET):
```
GET /akatools/traffic-report/download/?duration=LAST_1_WEEK&cpcode=1657476&limit=20
```

## Configuration Options

### Time Ranges
- `LAST_1_DAY`: Last 24 hours
- `LAST_1_WEEK`: Last 7 days
- `LAST_1_MONTH`: Last 30 days
- `LAST_3_MONTHS`: Last 90 days

### Dimensions
- `hostname`: Group by hostname
- `cpcode`: Group by CP code
- `country`: Group by country
- `responseClass`: Group by response class

### Metrics
- `edgeHitsSum`: Total edge hits
- `edgeBytesSum`: Total edge bytes
- `originHitsSum`: Total origin hits
- `originBytesSum`: Total origin bytes

### Filters
```python
filters = [
    {
        "dimensionName": "cpcode",
        "operator": "IN_LIST",
        "expressions": ["1657476", "1657477"]
    },
    {
        "dimensionName": "country",
        "operator": "EQUALS",
        "expressions": ["US"]
    }
]
```

## Chart Types

### 1. Bar Chart - Traffic by Bytes
- Shows top hostnames by edge bytes (GB)
- Color-coded by traffic volume
- Interactive hover tooltips

### 2. Bar Chart - Traffic by Hits
- Shows top hostnames by edge hits (millions)
- Green color scheme
- Sortable and filterable

### 3. Combined Chart - Bytes vs Hits
- Dual Y-axis visualization
- Bar chart for bytes, line chart for hits
- Comprehensive traffic overview

### 4. Pie Chart - Traffic Distribution
- Shows traffic distribution across hostnames
- Groups smaller hostnames into "Others"
- Percentage labels and hover details

## Error Handling

The module includes comprehensive error handling for:
- **Authentication failures**: Invalid credentials or network issues
- **API errors**: Rate limiting, invalid parameters, server errors
- **Data processing errors**: Malformed responses, missing fields
- **Chart generation errors**: Invalid data, rendering issues

## Testing

Run the comprehensive test suite:
```bash
python test_traffic_report.py
```

The test suite includes:
- Authentication testing
- Data fetching validation
- Chart generation verification
- Error handling validation

## Logging

The module uses Python's logging framework:
```python
import logging
logging.basicConfig(level=logging.INFO)
```

Log levels:
- `INFO`: General operation information
- `WARNING`: Non-critical issues
- `ERROR`: Error conditions
- `DEBUG`: Detailed debugging information

## Performance Considerations

- **Caching**: API responses can be cached for better performance
- **Limits**: Use appropriate limits to avoid large data transfers
- **Timeouts**: API requests have 30-second timeouts
- **Memory**: Large datasets are processed efficiently with pandas

## Security

- **Credentials**: Never hardcode API credentials
- **Sessions**: Proper session management and cleanup
- **Input Validation**: All user inputs are validated
- **Error Messages**: Sensitive information is not exposed in error messages

## Troubleshooting

### Common Issues:

1. **Authentication Failed**
   - Check `.edgerc2` file location and format
   - Verify API credentials are valid
   - Ensure network connectivity

2. **No Data Returned**
   - Verify CP code exists and is accessible
   - Check time range is valid
   - Confirm switch key permissions

3. **Chart Generation Failed**
   - Ensure Plotly is installed
   - Check data format and content
   - Verify sufficient data for visualization

4. **Web Interface Issues**
   - Ensure Django server is running
   - Check URL configuration
   - Verify user authentication

## Support

For issues and questions:
1. Check the logs for detailed error messages
2. Run the test suite to identify specific problems
3. Verify API credentials and permissions
4. Review the Django debug output for web interface issues
